package api

import (
	"anypanel/internal/config"
	"anypanel/internal/middleware"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// NewRouter 创建新的路由器
func NewRouter(cfg *config.Config, db *gorm.DB, rdb *redis.Client) *gin.Engine {
	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS中间件
	router.Use(func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		for _, allowedOrigin := range cfg.CORS.AllowedOrigins {
			if origin == allowedOrigin {
				c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
				break
			}
		}
		
		c.<PERSON>er("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "AnyTLS Panel is running",
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 设置认证路由
		SetupAuthRoutes(v1, db, cfg)

		// 管理员路由
		admin := v1.Group("/admin")
		admin.Use(middleware.AuthMiddleware(db), middleware.AdminMiddleware())
		{
			SetupAdminRoutes(admin, db)
		}

		// 用户路由
		user := v1.Group("/user")
		user.Use(middleware.AuthMiddleware(db))
		{
			SetupUserRoutes(user, db)
		}

		// V2bX兼容API路由
		SetupV2bXRoutes(v1, db)
	}

	return router
}