{"name": "v2board/v2board", "type": "project", "description": "v2board is a proxy protocol manage.", "keywords": ["v2board", "v2ray", "shadowsocks", "trojan", "laravel"], "license": "MIT", "require": {"php": "^7.3.0|^8.0", "fideloper/proxy": "^4.4", "firebase/php-jwt": "^6.3", "fruitcake/laravel-cors": "^2.0", "google/recaptcha": "^1.2", "guzzlehttp/guzzle": "^7.4.3", "laravel/framework": "^8.0", "laravel/horizon": "^5.9.6", "laravel/tinker": "^2.5", "linfo/linfo": "^4.0", "paragonie/sodium_compat": "^1.20", "php-curl-class/php-curl-class": "^8.6", "rybakit/msgpack": "^0.9.1", "stripe/stripe-php": "^v14.9.0", "symfony/yaml": "^4.3"}, "require-dev": {"facade/ignition": "^2.3.6", "fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.3", "phpunit/phpunit": "^9.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Library\\": "library/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "repositories": {"packagist": {"type": "composer", "url": "https://packagist.org"}}}