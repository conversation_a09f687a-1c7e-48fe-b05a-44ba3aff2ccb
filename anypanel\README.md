# AnyTLS Panel

一个基于AnyTLS协议的多用户管理面板，提供完整的用户管理、节点管理、流量统计和订阅生成功能。

## 🚀 快速开始

### 系统要求

- Go 1.21+
- Node.js 18+
- MySQL 8.0+
- Redis 7.0+

### 开发环境选择

本项目支持两种开发方式：

#### 🏠 本地开发（推荐）
直接使用本地安装的MySQL和Redis服务，无需Docker。

#### 🐳 Docker开发
使用Docker容器管理所有服务，适合需要环境隔离的场景。

---

## 🏠 本地开发（推荐）

### 1. 初始化数据库

#### Windows
```bash
# 运行数据库初始化脚本
.\scripts\init-db.bat
```

#### Linux/Mac
```bash
# 赋予执行权限并运行
chmod +x scripts/init-db.sh
./scripts/init-db.sh
```

### 2. 配置数据库密码

编辑 `backend/config.yaml` 文件，修改数据库密码：

```yaml
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "your_mysql_password"  # 修改为你的MySQL密码
  database: "anypanel"
```

### 3. 启动开发环境

#### Windows
```bash
# 一键启动本地开发环境
.\scripts\dev-local.bat
```

#### Linux/Mac
```bash
# 赋予执行权限并启动
chmod +x scripts/dev-local.sh
./scripts/dev-local.sh
```

### 4. 手动启动（可选）

如果脚本启动失败，可以手动启动：

1. **启动后端服务**
```bash
cd backend
go mod tidy
go run cmd/main.go
```

2. **启动前端服务**
```bash
cd frontend
npm install
npm run dev
```

---

## 🐳 Docker开发

### 一键启动开发环境

#### Windows 用户
```bash
# 使用Windows批处理脚本
.\scripts\dev-start.bat
```

#### Linux/Mac 用户
```bash
# 使用Shell脚本
chmod +x scripts/dev-start.sh
./scripts/dev-start.sh
```

### 使用Makefile
```bash
# 安装依赖
make install-deps

# 启动开发环境
make dev
```

### 手动启动

1. **启动数据库**
```bash
docker-compose up -d mysql redis
```

2. **初始化数据库**
```bash
docker-compose exec mysql mysql -uroot -prootpassword anypanel < scripts/init.sql
```

3. **启动后端服务**
```bash
cd backend
go mod tidy
go run cmd/main.go
```

4. **启动前端服务**
```bash
cd frontend
npm install
npm run dev
```

## 📊 访问地址

- **管理面板**: http://localhost:3000
- **API服务**: http://localhost:8080
- **数据库**: localhost:3306
- **Redis**: localhost:6379

## 🔑 默认账户

- **用户名**: admin
- **密码**: password

## 🛠️ 开发工具

### VS Code 插件推荐

- Go (golang.go)
- ESLint (dbaeumer.vscode-eslint)
- Prettier (esbenp.prettier-vscode)
- Docker (ms-azuretools.vscode-docker)
- GitLens (eamodio.gitlens)

### 热重载

#### 后端热重载
```bash
# 安装Air
go install github.com/cosmtrek/air@latest

# 启动热重载
cd backend
air -c ../.air.toml
```

#### 前端热重载
```bash
cd frontend
npm run dev
```

### 调试配置

使用VS Code的调试功能，配置文件位于 `.vscode/launch.json`。

## 📁 项目结构

```
anypanel/
├── backend/                 # 后端Go应用
│   ├── cmd/                # 命令行入口
│   ├── internal/           # 内部包
│   │   ├── api/           # API路由
│   │   ├── config/        # 配置管理
│   │   ├── database/      # 数据库连接
│   │   ├── model/         # 数据模型
│   │   ├── service/       # 业务逻辑
│   │   ├── middleware/    # 中间件
│   │   └── utils/         # 工具函数
│   ├── migrations/         # 数据库迁移
│   ├── scripts/           # 脚本文件
│   ├── go.mod             # Go模块
│   └── config.yaml        # 配置文件
├── frontend/               # 前端React应用
│   ├── src/
│   │   ├── components/    # 组件
│   │   ├── pages/         # 页面
│   │   ├── services/      # API服务
│   │   ├── utils/         # 工具函数
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── stores/        # 状态管理
│   │   ├── types/         # 类型定义
│   │   └── constants/     # 常量定义
│   ├── public/            # 静态资源
│   ├── package.json       # Node.js依赖
│   └── vite.config.ts     # Vite配置
├── docker/                # Docker配置
├── scripts/               # 脚本文件
├── docs/                  # 文档
├── docker-compose.yml     # Docker编排
└── Makefile              # 构建脚本
```

## 🐳 Docker 部署

### 开发环境
```bash
docker-compose up -d
```

### 生产环境
```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🔧 配置说明

### 后端配置
配置文件位于 `backend/config.yaml`，包含以下配置：

- **数据库配置**: MySQL连接信息
- **Redis配置**: Redis连接信息
- **JWT配置**: 认证密钥和过期时间
- **日志配置**: 日志级别和文件路径
- **跨域配置**: CORS设置
- **限流配置**: API限流设置

### 前端配置
环境变量文件位于 `frontend/.env.*`，包含以下配置：

- **API地址**: 后端服务地址
- **应用标题**: 应用名称
- **环境标识**: 开发/生产环境

## 📚 API文档

### 管理员API
```
GET    /api/admin/users              # 获取用户列表
POST   /api/admin/users              # 创建用户
PUT    /api/admin/users/:id          # 更新用户
DELETE /api/admin/users/:id          # 删除用户

GET    /api/admin/nodes              # 获取节点列表
POST   /api/admin/nodes              # 创建节点
PUT    /api/admin/nodes/:id          # 更新节点
DELETE /api/admin/nodes/:id          # 删除节点
```

### 用户API
```
GET    /api/user/profile             # 获取用户资料
PUT    /api/user/profile             # 更新用户资料
GET    /api/user/subscription        # 获取订阅信息

GET    /api/user/subscription/clash          # Clash订阅
GET    /api/user/subscription/sing-box       # Sing-box订阅
GET    /api/user/subscription/v2ray          # V2Ray订阅
```

### V2bX兼容API
```
GET    /api/v1/server/UniProxy/config     # 获取节点配置
GET    /api/v1/server/UniProxy/user       # 获取用户列表
POST   /api/v1/server/UniProxy/push       # 上报流量
POST   /api/v1/server/UniProxy/alive      # 上报在线用户
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题，请：

1. 查看 [文档](docs/)
2. 搜索已有的 [Issues](issues)
3. 创建新的 Issue 描述问题

## 🎯 路线图

- [x] 项目初始化和基础架构搭建
- [x] 数据库设计和模型实现
- [x] 本地开发环境配置
- [ ] 后端API核心服务实现
- [ ] 前端管理界面开发
- [ ] 用户界面开发
- [ ] V2bX兼容API实现
- [ ] 流量统计和监控系统
- [ ] 订阅生成和配置管理
- [ ] 商品系统实现
- [ ] 权限组系统实现
- [ ] 系统安全和性能优化
- [ ] 文档和部署指南

## 📚 详细文档

- [本地开发指南](docs/local-development.md)
- [API文档](docs/api.md)
- [部署指南](docs/deployment.md)
- [常见问题](docs/faq.md)