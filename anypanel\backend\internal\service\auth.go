package service

import (
	"anypanel/internal/config"
	"anypanel/internal/middleware"
	"anypanel/internal/model"
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AuthService struct {
	db  *gorm.DB
	cfg *config.Config
}

func NewAuthService(db *gorm.DB, cfg *config.Config) *AuthService {
	return &AuthService{
		db:  db,
		cfg: cfg,
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken  string     `json:"access_token"`
	RefreshToken string     `json:"refresh_token"`
	ExpiresIn    int        `json:"expires_in"`
	User         *UserDTO   `json:"user"`
}

// UserDTO 用户数据传输对象
type UserDTO struct {
	ID           uint      `json:"id"`
	Username     string    `json:"username"`
	Email        string    `json:"email"`
	Role         string    `json:"role"`
	Status       string    `json:"status"`
	TrafficLimit int64     `json:"traffic_limit"`
	TrafficUsed  int64     `json:"traffic_used"`
	DeviceLimit  int       `json:"device_limit"`
	SpeedLimit   int       `json:"speed_limit"`
	ExpiredAt    *time.Time `json:"expired_at"`
	CreatedAt    time.Time `json:"created_at"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// RefreshTokenRequest 刷新token请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// Login 用户登录
func (s *AuthService) Login(req *LoginRequest) (*LoginResponse, error) {
	// 查找用户
	var user model.User
	if err := s.db.Where("username = ? OR email = ?", req.Username, req.Username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户名或密码错误")
		}
		return nil, err
	}

	// 检查用户状态
	if user.Status != "active" {
		return nil, errors.New("用户账户已被禁用")
	}

	// 检查用户是否过期
	if user.ExpiredAt != nil && user.ExpiredAt.Before(time.Now()) {
		return nil, errors.New("用户账户已过期")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 生成token
	accessToken, err := GenerateAccessToken(&user, s.cfg)
	if err != nil {
		return nil, err
	}

	refreshToken, err := GenerateRefreshToken(&user, s.cfg)
	if err != nil {
		return nil, err
	}

	// 转换用户数据
	userDTO := s.toUserDTO(&user)

	return &LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    s.cfg.JWT.ExpireTime,
		User:         userDTO,
	}, nil
}

// Register 用户注册
func (s *AuthService) Register(req *RegisterRequest) (*UserDTO, error) {
	// 检查用户名是否已存在
	var existingUser model.User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("邮箱已存在")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := &model.User{
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: string(hashedPassword),
		Role:         "user",
		Status:       "active",
		TrafficLimit: 0, // 默认无限制
		TrafficUsed:  0,
		DeviceLimit:  1,
		SpeedLimit:   0, // 默认无限制
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, err
	}

	return s.toUserDTO(user), nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID uint, req *ChangePasswordRequest) error {
	// 查找用户
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.OldPassword)); err != nil {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	user.PasswordHash = string(hashedPassword)
	if err := s.db.Save(&user).Error; err != nil {
		return err
	}

	return nil
}

// RefreshToken 刷新token
func (s *AuthService) RefreshToken(req *RefreshTokenRequest) (*LoginResponse, error) {
	// 解析refresh token
	claims, err := ParseRefreshToken(req.RefreshToken)
	if err != nil {
		return nil, errors.New("无效的刷新token")
	}

	// 查找用户
	var user model.User
	if err := s.db.First(&user, claims.UserID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 检查用户状态
	if user.Status != "active" {
		return nil, errors.New("用户账户已被禁用")
	}

	// 生成新的token
	accessToken, err := GenerateAccessToken(&user, s.cfg)
	if err != nil {
		return nil, err
	}

	refreshToken, err := GenerateRefreshToken(&user, s.cfg)
	if err != nil {
		return nil, err
	}

	// 转换用户数据
	userDTO := s.toUserDTO(&user)

	return &LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    s.cfg.JWT.ExpireTime,
		User:         userDTO,
	}, nil
}

// GetUserProfile 获取用户资料
func (s *AuthService) GetUserProfile(userID uint) (*UserDTO, error) {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	return s.toUserDTO(&user), nil
}

// UpdateUserProfile 更新用户资料
func (s *AuthService) UpdateUserProfile(userID uint, updates map[string]interface{}) (*UserDTO, error) {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, errors.New("用户不存在")
	}

	// 不允许更新的字段
	delete(updates, "password_hash")
	delete(updates, "role")
	delete(updates, "status")
	delete(updates, "traffic_used")

	if err := s.db.Model(&user).Updates(updates).Error; err != nil {
		return nil, err
	}

	return s.toUserDTO(&user), nil
}

// 辅助函数
func (s *AuthService) toUserDTO(user *model.User) *UserDTO {
	return &UserDTO{
		ID:           user.ID,
		Username:     user.Username,
		Email:        user.Email,
		Role:         user.Role,
		Status:       user.Status,
		TrafficLimit: user.TrafficLimit,
		TrafficUsed:  user.TrafficUsed,
		DeviceLimit:  user.DeviceLimit,
		SpeedLimit:   user.SpeedLimit,
		ExpiredAt:    user.ExpiredAt,
		CreatedAt:    user.CreatedAt,
	}
}

// 生成访问token
func GenerateAccessToken(user *model.User, cfg *config.Config) (string, error) {
	claims := &middleware.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(cfg.JWT.ExpireTime) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "anypanel",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.JWT.Secret))
}

// 生成刷新token
func GenerateRefreshToken(user *model.User, cfg *config.Config) (string, error) {
	claims := &middleware.JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(cfg.JWT.RefreshExpireTime) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "anypanel",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.JWT.Secret))
}

// 解析刷新token
func ParseRefreshToken(tokenString string) (*middleware.JWTClaims, error) {
	claims := &middleware.JWTClaims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte("anypanel-secret-key"), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("无效的token")
	}

	return claims, nil
}