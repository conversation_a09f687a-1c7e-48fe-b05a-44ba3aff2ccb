package service

import (
	"anypanel/internal/model"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type OnlineUserService struct {
	db           *gorm.DB
	userService  *UserService
	nodeService  *NodeService
	trafficService *TrafficService
}

func NewOnlineUserService(db *gorm.DB, userService *UserService, nodeService *NodeService, trafficService *TrafficService) *OnlineUserService {
	return &OnlineUserService{
		db:            db,
		userService:   userService,
		nodeService:   nodeService,
		trafficService: trafficService,
	}
}

// OnlineUserStats 在线用户统计
type OnlineUserStats struct {
	TotalUsers      int64                    `json:"total_users"`
	UsersByNode     map[uint]int64           `json:"users_by_node"`
	UsersByStatus   map[string]int64         `json:"users_by_status"`
	TopUsers        []OnlineUserDetail       `json:"top_users"`
	Connections      []OnlineConnectionDetail `json:"connections"`
}

// OnlineUserDetail 在线用户详情
type OnlineUserDetail struct {
	UserID        uint       `json:"user_id"`
	Username      string     `json:"username"`
	Email         string     `json:"email"`
	ConnectionCount int64     `json:"connection_count"`
	ConnectedAt   time.Time  `json:"connected_at"`
	LastSeen      time.Time  `json:"last_seen"`
}

// OnlineConnectionDetail 在线连接详情
type OnlineConnectionDetail struct {
	ID           uint      `json:"id"`
	UserID       uint      `json:"user_id"`
	Username     string    `json:"username"`
	NodeID       uint      `json:"node_id"`
	NodeName     string    `json:"node_name"`
	IPAddress    string    `json:"ip_address"`
	ConnectedAt  time.Time `json:"connected_at"`
	LastSeen     time.Time `json:"last_seen"`
	Duration     string    `json:"duration"`
}

// ConnectionLimitCheck 连接限制检查结果
type ConnectionLimitCheck struct {
	UserID        uint   `json:"user_id"`
	Username      string `json:"username"`
	CurrentCount  int64  `json:"current_count"`
	MaxAllowed    int    `json:"max_allowed"`
	CanConnect    bool   `json:"can_connect"`
	ViolationType string `json:"violation_type,omitempty"`
}

// ProcessV2bXAliveReport 处理V2bX在线用户上报
func (s *OnlineUserService) ProcessV2bXAliveReport(nodeToken string, reports []AliveReport) error {
	return s.trafficService.v2bxService.ReportAlive(nodeToken, reports)
}

// GetOnlineUserStats 获取在线用户统计
func (s *OnlineUserService) GetOnlineUserStats() (*OnlineUserStats, error) {
	// 获取最近5分钟内有活动的在线用户
	recentTime := time.Now().Add(-5 * time.Minute)
	
	var onlineUsers []model.OnlineUser
	if err := s.db.Where("last_seen >= ?", recentTime).
		Preload("User").
		Preload("Node").
		Find(&onlineUsers).Error; err != nil {
		return nil, err
	}

	stats := &OnlineUserStats{
		TotalUsers:    int64(len(onlineUsers)),
		UsersByNode:   make(map[uint]int64),
		UsersByStatus: make(map[string]int64),
		TopUsers:      make([]OnlineUserDetail, 0),
		Connections:   make([]OnlineConnectionDetail, 0),
	}

	// 按节点统计
	userCountByNode := make(map[uint]int64)
	for _, user := range onlineUsers {
		userCountByNode[user.NodeID]++
	}
	stats.UsersByNode = userCountByNode

	// 按用户状态统计
	statusCount := make(map[string]int64)
	userConnectionCount := make(map[uint]int64)
	
	for _, onlineUser := range onlineUsers {
		// 统计用户状态
		status := onlineUser.User.Status
		if onlineUser.User.IsExpired() {
			status = "expired"
		} else if !onlineUser.User.CanConnect() {
			status = "limited"
		}
		statusCount[status]++
		
		// 统计用户连接数
		userConnectionCount[onlineUser.UserID]++
	}
	stats.UsersByStatus = statusCount

	// 获取连接最多的用户
	for userID, count := range userConnectionCount {
		user, err := s.userService.GetUserByID(userID)
		if err != nil {
			continue
		}
		
		// 获取该用户的最早连接时间
		var firstConnection model.OnlineUser
		if err := s.db.Where("user_id = ?", userID).
			Order("connected_at ASC").
			First(&firstConnection).Error; err != nil {
			continue
		}
		
		userDetail := OnlineUserDetail{
			UserID:         user.ID,
			Username:       user.Username,
			Email:          user.Email,
			ConnectionCount: count,
			ConnectedAt:    firstConnection.ConnectedAt,
			LastSeen:       time.Now(), // 使用当前时间作为最后活跃时间
		}
		stats.TopUsers = append(stats.TopUsers, userDetail)
	}

	// 获取所有连接详情
	for _, onlineUser := range onlineUsers {
		duration := onlineUser.LastSeen.Sub(onlineUser.ConnectedAt)
		
		connection := OnlineConnectionDetail{
			ID:          onlineUser.ID,
			UserID:      onlineUser.UserID,
			Username:    onlineUser.User.Username,
			NodeID:      onlineUser.NodeID,
			NodeName:    onlineUser.Node.Name,
			IPAddress:   onlineUser.IPAddress,
			ConnectedAt: onlineUser.ConnectedAt,
			LastSeen:    onlineUser.LastSeen,
			Duration:    s.formatDuration(duration),
		}
		stats.Connections = append(stats.Connections, connection)
	}

	return stats, nil
}

// CheckConnectionLimits 检查用户连接限制
func (s *OnlineUserService) CheckConnectionLimits() ([]ConnectionLimitCheck, error) {
	// 获取所有活跃用户
	users, err := s.userService.GetActiveUsers()
	if err != nil {
		return nil, err
	}

	var checks []ConnectionLimitCheck
	for _, user := range users {
		// 统计当前连接数
		var currentCount int64
		if err := s.db.Model(&model.OnlineUser{}).
			Where("user_id = ? AND last_seen >= ?", user.ID, time.Now().Add(-5*time.Minute)).
			Count(&currentCount).Error; err != nil {
			continue
		}

		check := ConnectionLimitCheck{
			UserID:       user.ID,
			Username:     user.Username,
			CurrentCount: currentCount,
			MaxAllowed:   user.DeviceLimit,
			CanConnect:   true,
		}

		// 检查是否超过限制
		if user.DeviceLimit > 0 && currentCount >= int64(user.DeviceLimit) {
			check.CanConnect = false
			check.ViolationType = "device_limit_exceeded"
		}

		// 检查用户状态
		if !user.CanConnect() {
			check.CanConnect = false
			check.ViolationType = "user_limited"
		}

		checks = append(checks, check)
	}

	return checks, nil
}

// CleanupExpiredConnections 清理过期连接
func (s *OnlineUserService) CleanupExpiredConnections() error {
	// 清理超过10分钟未更新的连接
	cutoffTime := time.Now().Add(-10 * time.Minute)
	if err := s.db.Where("last_seen < ?", cutoffTime).
		Delete(&model.OnlineUser{}).Error; err != nil {
		return fmt.Errorf("清理过期连接失败: %v", err)
	}

	// 清理已过期用户的连接
	var expiredUserIDs []uint
	if err := s.db.Model(&model.User{}).
		Where("status = ? OR (expired_at IS NOT NULL AND expired_at < NOW())", "expired").
		Pluck("id", &expiredUserIDs).Error; err != nil {
		return fmt.Errorf("获取过期用户失败: %v", err)
	}

	if len(expiredUserIDs) > 0 {
		if err := s.db.Where("user_id IN ?", expiredUserIDs).
			Delete(&model.OnlineUser{}).Error; err != nil {
			return fmt.Errorf("清理过期用户连接失败: %v", err)
		}
	}

	return nil
}

// GetUserOnlineConnections 获取用户的在线连接
func (s *OnlineUserService) GetUserOnlineConnections(userID uint) ([]OnlineConnectionDetail, error) {
	var onlineUsers []model.OnlineUser
	if err := s.db.Where("user_id = ? AND last_seen >= ?", userID, time.Now().Add(-5*time.Minute)).
		Preload("Node").
		Find(&onlineUsers).Error; err != nil {
		return nil, err
	}

	connections := make([]OnlineConnectionDetail, 0, len(onlineUsers))
	for _, onlineUser := range onlineUsers {
		duration := onlineUser.LastSeen.Sub(onlineUser.ConnectedAt)
		
		connection := OnlineConnectionDetail{
			ID:          onlineUser.ID,
			UserID:      onlineUser.UserID,
			Username:    onlineUser.User.Username,
			NodeID:      onlineUser.NodeID,
			NodeName:    onlineUser.Node.Name,
			IPAddress:   onlineUser.IPAddress,
			ConnectedAt: onlineUser.ConnectedAt,
			LastSeen:    onlineUser.LastSeen,
			Duration:    s.formatDuration(duration),
		}
		connections = append(connections, connection)
	}

	return connections, nil
}

// GetNodeOnlineUsers 获取节点的在线用户
func (s *OnlineUserService) GetNodeOnlineUsers(nodeID uint) ([]OnlineConnectionDetail, error) {
	var onlineUsers []model.OnlineUser
	if err := s.db.Where("node_id = ? AND last_seen >= ?", nodeID, time.Now().Add(-5*time.Minute)).
		Preload("User").
		Find(&onlineUsers).Error; err != nil {
		return nil, err
	}

	connections := make([]OnlineConnectionDetail, 0, len(onlineUsers))
	for _, onlineUser := range onlineUsers {
		duration := onlineUser.LastSeen.Sub(onlineUser.ConnectedAt)
		
		connection := OnlineConnectionDetail{
			ID:          onlineUser.ID,
			UserID:      onlineUser.UserID,
			Username:    onlineUser.User.Username,
			NodeID:      onlineUser.NodeID,
			NodeName:    onlineUser.Node.Name,
			IPAddress:   onlineUser.IPAddress,
			ConnectedAt: onlineUser.ConnectedAt,
			LastSeen:    onlineUser.LastSeen,
			Duration:    s.formatDuration(duration),
		}
		connections = append(connections, connection)
	}

	return connections, nil
}

// DisconnectUser 强制断开用户连接
func (s *OnlineUserService) DisconnectUser(userID uint, nodeID *uint, ipAddress *string) error {
	query := s.db.Where("user_id = ?", userID)
	
	if nodeID != nil {
		query = query.Where("node_id = ?", *nodeID)
	}
	
	if ipAddress != nil {
		query = query.Where("ip_address = ?", *ipAddress)
	}

	if err := query.Delete(&model.OnlineUser{}).Error; err != nil {
		return fmt.Errorf("断开用户连接失败: %v", err)
	}

	return nil
}

// GetConnectionHistory 获取连接历史
func (s *OnlineUserService) GetConnectionHistory(userID uint, limit int) ([]OnlineConnectionDetail, error) {
	if limit <= 0 {
		limit = 50 // 默认50条记录
	}

	var onlineUsers []model.OnlineUser
	if err := s.db.Where("user_id = ?", userID).
		Preload("Node").
		Order("last_seen DESC").
		Limit(limit).
		Find(&onlineUsers).Error; err != nil {
		return nil, err
	}

	history := make([]OnlineConnectionDetail, 0, len(onlineUsers))
	for _, onlineUser := range onlineUsers {
		duration := onlineUser.LastSeen.Sub(onlineUser.ConnectedAt)
		
		connection := OnlineConnectionDetail{
			ID:          onlineUser.ID,
			UserID:      onlineUser.UserID,
			Username:    onlineUser.User.Username,
			NodeID:      onlineUser.NodeID,
			NodeName:    onlineUser.Node.Name,
			IPAddress:   onlineUser.IPAddress,
			ConnectedAt: onlineUser.ConnectedAt,
			LastSeen:    onlineUser.LastSeen,
			Duration:    s.formatDuration(duration),
		}
		history = append(history, connection)
	}

	return history, nil
}

// MonitorConnectionViolations 监控连接违规
func (s *OnlineUserService) MonitorConnectionViolations() ([]ConnectionLimitCheck, error) {
	checks, err := s.CheckConnectionLimits()
	if err != nil {
		return nil, err
	}

	// 筛选出违规的检查
	var violations []ConnectionLimitCheck
	for _, check := range checks {
		if !check.CanConnect {
			violations = append(violations, check)
		}
	}

	return violations, nil
}

// GetRealtimeConnectionStats 获取实时连接统计
func (s *OnlineUserService) GetRealtimeConnectionStats() (map[string]interface{}, error) {
	// 获取最近5分钟的在线用户
	recentTime := time.Now().Add(-5 * time.Minute)
	
	var onlineCount int64
	if err := s.db.Model(&model.OnlineUser{}).
		Where("last_seen >= ?", recentTime).
		Count(&onlineCount).Error; err != nil {
		return nil, err
	}

	// 获取总连接数
	var totalConnections int64
	if err := s.db.Model(&model.OnlineUser{}).
		Count(&totalConnections).Error; err != nil {
		return nil, err
	}

	// 获取各节点的在线用户数
	var nodeStats []struct {
		NodeID   uint   `json:"node_id"`
		NodeName string `json:"node_name"`
		Count    int64  `json:"count"`
	}

	if err := s.db.Model(&model.OnlineUser{}).
		Select("node_id, COUNT(*) as count").
		Where("last_seen >= ?", recentTime).
		Group("node_id").
		Joins("LEFT JOIN nodes ON nodes.id = online_users.node_id").
		Scan(&nodeStats).Error; err != nil {
		return nil, err
	}

	// 构建节点统计映射
	nodesByCount := make(map[string]int64)
	for _, stat := range nodeStats {
		nodesByCount[stat.NodeName] = stat.Count
	}

	// 获取连接最多的用户
	var topUsers []struct {
		UserID    uint   `json:"user_id"`
		Username  string `json:"username"`
		Count     int64  `json:"count"`
	}

	if err := s.db.Model(&model.OnlineUser{}).
		Select("user_id, COUNT(*) as count").
		Where("last_seen >= ?", recentTime).
		Group("user_id").
		Joins("LEFT JOIN users ON users.id = online_users.user_id").
		Order("count DESC").
		Limit(10).
		Scan(&topUsers).Error; err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"online_users":      onlineCount,
		"total_connections": totalConnections,
		"nodes_by_count":    nodesByCount,
		"top_users":         topUsers,
		"updated_at":        time.Now().Format("2006-01-02 15:04:05"),
	}

	return stats, nil
}

// formatDuration 格式化持续时间
func (s *OnlineUserService) formatDuration(duration time.Duration) string {
	if duration < time.Minute {
		return fmt.Sprintf("%d秒", int(duration.Seconds()))
	} else if duration < time.Hour {
		return fmt.Sprintf("%d分%d秒", int(duration.Minutes()), int(duration.Seconds())%60)
	} else {
		hours := int(duration.Hours())
		minutes := int(duration.Minutes()) % 60
		return fmt.Sprintf("%d小时%d分", hours, minutes)
	}
}

// UpdateUserLastSeen 更新用户最后活跃时间
func (s *OnlineUserService) UpdateUserLastSeen(userID uint, nodeID uint, ipAddress string) error {
	// 查找现有的在线记录
	var onlineUser model.OnlineUser
	if err := s.db.Where("user_id = ? AND node_id = ? AND ip_address = ?", userID, nodeID, ipAddress).
		First(&onlineUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新的在线记录
			onlineUser = model.OnlineUser{
				UserID:      userID,
				NodeID:      nodeID,
				IPAddress:   ipAddress,
				ConnectedAt: time.Now(),
				LastSeen:    time.Now(),
			}
			return s.db.Create(&onlineUser).Error
		}
		return err
	}

	// 更新现有记录
	onlineUser.LastSeen = time.Now()
	return s.db.Save(&onlineUser).Error
}

// ValidateUserConnection 验证用户连接权限
func (s *OnlineUserService) ValidateUserConnection(userID uint, nodeID uint, ipAddress string) error {
	// 检查用户状态
	user, err := s.userService.GetUserByID(userID)
	if err != nil {
		return err
	}

	if !user.CanConnect() {
		return errors.New("用户状态不允许连接")
	}

	// 检查设备限制
	var currentCount int64
	if err := s.db.Model(&model.OnlineUser{}).
		Where("user_id = ? AND node_id = ? AND last_seen >= ?", userID, nodeID, time.Now().Add(-5*time.Minute)).
		Count(&currentCount).Error; err != nil {
		return err
	}

	if user.DeviceLimit > 0 && currentCount >= int64(user.DeviceLimit) {
		return errors.New("超过设备限制")
	}

	// 检查节点状态
	node, err := s.nodeService.GetNodeByID(nodeID)
	if err != nil {
		return err
	}

	if node.Status != "online" {
		return errors.New("节点不在线")
	}

	// 检查节点用户限制
	if node.MaxUsers > 0 {
		var nodeUserCount int64
		if err := s.db.Model(&model.OnlineUser{}).
			Where("node_id = ? AND last_seen >= ?", nodeID, time.Now().Add(-5*time.Minute)).
			Count(&nodeUserCount).Error; err != nil {
			return err
		}

		if nodeUserCount >= int64(node.MaxUsers) {
			return errors.New("节点用户数已满")
		}
	}

	return nil
}