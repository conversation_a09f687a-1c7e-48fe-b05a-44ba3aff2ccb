package service

import (
	"anypanel/internal/model"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type V2bXService struct {
	db             *gorm.DB
	nodeService    *NodeService
	userService    *UserService
	serverToken    string
}

func NewV2bXService(db *gorm.DB, nodeService *NodeService, userService *UserService, serverToken string) *V2bXService {
	return &V2bXService{
		db:          db,
		nodeService: nodeService,
		userService: userService,
		serverToken: serverToken,
	}
}

// AnyTLS配置结构
type AnyTLSNodeConfig struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Host        string `json:"host"`
	Port        int    `json:"port"`
	Password    string `json:"password"`
	ServerName  string `json:"server_name,omitempty"`
	PaddingScheme []string `json:"padding_scheme,omitempty"`
	TrafficRate float64 `json:"traffic_rate"`
}

// UniProxy配置响应
type UniProxyConfigResponse struct {
	Nodes      []AnyTLSNodeConfig `json:"nodes"`
	TrafficRate float64           `json:"traffic_rate"`
	ServerName  string            `json:"server_name,omitempty"`
}

// 用户信息结构
type UniProxyUserInfo struct {
	UUID        string `json:"uuid"`
	Password    string `json:"password"`
	TrafficUsed  int64  `json:"traffic_used"`
	TrafficLimit int64  `json:"traffic_limit"`
	SpeedLimit  int    `json:"speed_limit"`
	DeviceLimit int    `json:"device_limit"`
	ExpiredAt   *time.Time `json:"expired_at,omitempty"`
}

// 用户列表响应
type UniProxyUserResponse struct {
	Users []UniProxyUserInfo `json:"users"`
}

// 流量上报结构
type TrafficReport struct {
	UUID   string `json:"uuid"`
	Upload int64  `json:"upload"`
	Download int64 `json:"download"`
}

// 在线用户上报结构
type AliveReport struct {
	UUID      string `json:"uuid"`
	IPAddress string `json:"ip_address"`
}

// 在线统计响应
type AliveListResponse struct {
	Users []string `json:"users"`
	Count int      `json:"count"`
}

// GetNodeConfig 获取节点配置
func (s *V2bXService) GetNodeConfig(token, nodeType string, nodeID uint) (map[string]interface{}, error) {
	// 验证服务器token
	if err := s.validateServerToken(token); err != nil {
		return nil, err
	}

	// 根据ID和类型获取节点配置
	node, err := s.getNodeByIDAndType(nodeID, nodeType)
	if err != nil {
		return nil, err
	}

	// 检查节点状态
	if node.Status != "online" {
		return nil, errors.New("节点已离线")
	}

	config, err := s.generateNodeConfigByType(node, nodeType)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// GetNodeUsers 获取节点用户列表
func (s *V2bXService) GetNodeUsers(token, nodeType string, nodeID uint) (*UniProxyUserResponse, error) {
	// 验证服务器token
	if err := s.validateServerToken(token); err != nil {
		return nil, err
	}

	// 根据ID和类型获取节点
	node, err := s.getNodeByIDAndType(nodeID, nodeType)
	if err != nil {
		return nil, err
	}

	// 检查节点状态
	if node.Status != "online" {
		return nil, errors.New("节点已离线")
	}

	// 获取该节点的用户列表
	users, err := s.getNodeUsers(node)
	if err != nil {
		return nil, err
	}

	return &UniProxyUserResponse{
		Users: users,
	}, nil
}

// ReportTraffic 处理流量上报
func (s *V2bXService) ReportTraffic(token, nodeType string, nodeID uint, reports []TrafficReport) error {
	// 验证服务器token
	if err := s.validateServerToken(token); err != nil {
		return err
	}

	// 根据ID和类型获取节点
	node, err := s.getNodeByIDAndType(nodeID, nodeType)
	if err != nil {
		return err
	}

	// 检查节点状态
	if node.Status != "online" {
		return errors.New("节点已离线")
	}

	// 处理流量数据
	return s.processTrafficReports(node, reports)
}

// ReportAlive 处理在线用户上报
func (s *V2bXService) ReportAlive(token, nodeType string, nodeID uint, reports []AliveReport) error {
	// 验证服务器token
	if err := s.validateServerToken(token); err != nil {
		return err
	}

	// 根据ID和类型获取节点
	node, err := s.getNodeByIDAndType(nodeID, nodeType)
	if err != nil {
		return err
	}

	// 检查节点状态
	if node.Status != "online" {
		return errors.New("节点已离线")
	}

	// 处理在线用户数据
	return s.processAliveReports(node, reports)
}

// GetAliveList 获取在线统计
func (s *V2bXService) GetAliveList(token, nodeType string, nodeID uint) (*AliveListResponse, error) {
	// 验证服务器token
	if err := s.validateServerToken(token); err != nil {
		return nil, err
	}

	// 根据ID和类型获取节点
	node, err := s.getNodeByIDAndType(nodeID, nodeType)
	if err != nil {
		return nil, err
	}

	// 检查节点状态
	if node.Status != "online" {
		return nil, errors.New("节点已离线")
	}

	// 获取在线用户统计
	return s.getAliveList(node)
}

// 验证服务器token
func (s *V2bXService) validateServerToken(token string) error {
	if token == "" {
		return errors.New("token不能为空")
	}

	if token != s.serverToken {
		return errors.New("token验证失败")
	}

	return nil
}

// 生成节点配置
func (s *V2bXService) generateNodeConfig(node *model.Node) (map[string]interface{}, error) {
	// 解析AnyTLS特定配置
	var anytlsConfig struct {
		PaddingScheme []string `json:"padding_scheme,omitempty"`
	}
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &anytlsConfig); err != nil {
			return nil, fmt.Errorf("解析节点配置失败: %v", err)
		}
	}

	// 构建v2board风格的配置响应
	response := map[string]interface{}{
		"server_port":    node.Port,
		"server_name":    node.ServerName,
		"padding_scheme": anytlsConfig.PaddingScheme,
		"base_config": map[string]interface{}{
			"push_interval": 60,
			"pull_interval": 60,
		},
	}

	return response, nil
}

// 获取节点用户列表
func (s *V2bXService) getNodeUsers(node *model.Node) ([]UniProxyUserInfo, error) {
	// 基于权限组系统获取用户
	// 获取可以访问该节点的权限组
	var permissionGroups []model.PermissionGroup
	if err := s.db.Joins("JOIN permission_group_nodes ON permission_group_nodes.group_id = permission_groups.id").
		Where("permission_group_nodes.node_id = ?", node.ID).
		Find(&permissionGroups).Error; err != nil {
		return nil, err
	}

	// 如果没有权限组，返回空列表
	if len(permissionGroups) == 0 {
		return []UniProxyUserInfo{}, nil
	}

	// 获取这些权限组对应的活跃用户
	var users []model.User
	if err := s.db.Joins("JOIN user_subscriptions ON user_subscriptions.user_id = users.id").
		Joins("JOIN products ON products.id = user_subscriptions.product_id").
		Where("products.permission_group_id IN ? AND users.status = ? AND user_subscriptions.status = ? AND user_subscriptions.expired_at > ?", 
			getPermissionGroupIDs(permissionGroups), "active", "active", time.Now()).
		Find(&users).Error; err != nil {
		return nil, err
	}

	var userInfos []UniProxyUserInfo
	for _, user := range users {
		// 检查用户是否可以连接（流量限制、设备限制等）
		if user.IsActive() && user.CanConnect() {
			userInfo := UniProxyUserInfo{
				UUID:         user.UUID,
				Password:     "", // V2bX不需要密码，使用UUID验证
				TrafficUsed:  user.TrafficUsed,
				TrafficLimit: user.TrafficLimit,
				SpeedLimit:   user.SpeedLimit,
				DeviceLimit:  user.DeviceLimit,
				ExpiredAt:    user.ExpiredAt,
			}
			userInfos = append(userInfos, userInfo)
		}
	}

	return userInfos, nil
}

// 处理流量上报
func (s *V2bXService) processTrafficReports(node *model.Node, reports []TrafficReport) error {
	// 批量处理流量数据
	trafficLogs := make([]model.TrafficLog, 0, len(reports))
	
	for _, report := range reports {
		// 根据UUID获取用户
		user, err := s.userService.GetUserByUUID(report.UUID)
		if err != nil {
			continue // 跳过无效用户
		}

		// 创建流量日志
		trafficLog := model.TrafficLog{
			UserID:     user.ID,
			NodeID:     node.ID,
			Upload:     report.Upload,
			Download:   report.Download,
			RecordedAt: time.Now(),
		}
		trafficLogs = append(trafficLogs, trafficLog)

		// 更新用户流量使用情况
		user.TrafficUsed += report.Upload + report.Download
		
		// 检查流量限制
		if user.TrafficLimit > 0 && user.TrafficUsed >= user.TrafficLimit {
			user.Status = "expired"
		}
		
		if err := s.db.Save(user).Error; err != nil {
			continue // 保存失败时继续处理其他用户
		}
	}

	// 批量插入流量日志
	if len(trafficLogs) > 0 {
		if err := s.db.CreateInBatches(trafficLogs, 100).Error; err != nil {
			return fmt.Errorf("保存流量日志失败: %v", err)
		}
	}

	return nil
}

// 处理在线用户上报
func (s *V2bXService) processAliveReports(node *model.Node, reports []AliveReport) error {
	// 清理该节点的旧在线记录（超过5分钟未更新）
	cutoffTime := time.Now().Add(-5 * time.Minute)
	if err := s.db.Where("node_id = ? AND last_seen < ?", node.ID, cutoffTime).
		Delete(&model.OnlineUser{}).Error; err != nil {
		return fmt.Errorf("清理过期在线记录失败: %v", err)
	}

	// 获取可以访问该节点的权限组
	var permissionGroups []model.PermissionGroup
	if err := s.db.Joins("JOIN permission_group_nodes ON permission_group_nodes.group_id = permission_groups.id").
		Where("permission_group_nodes.node_id = ?", node.ID).
		Find(&permissionGroups).Error; err != nil {
		return fmt.Errorf("获取节点权限组失败: %v", err)
	}

	// 如果没有权限组，直接返回
	if len(permissionGroups) == 0 {
		return nil
	}

	// 处理新的在线用户上报
	for _, report := range reports {
		// 根据UUID获取用户
		user, err := s.userService.GetUserByUUID(report.UUID)
		if err != nil {
			continue // 跳过无效用户
		}

		// 检查用户是否有权限访问该节点
		if !s.hasNodeAccess(user, permissionGroups) {
			continue // 跳过无权限用户
		}

		// 检查用户状态
		if !user.IsActive() || !user.CanConnect() {
			continue // 跳过非活跃用户
		}

		// 检查设备限制
		if !s.checkDeviceLimit(user.ID, node.ID, report.IPAddress) {
			continue // 跳过超过设备限制的用户
		}

		// 更新或创建在线用户记录
		var onlineUser model.OnlineUser
		if err := s.db.Where("user_id = ? AND node_id = ? AND ip_address = ?", 
			user.ID, node.ID, report.IPAddress).First(&onlineUser).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 创建新的在线记录
				onlineUser = model.OnlineUser{
					UserID:      user.ID,
					NodeID:      node.ID,
					IPAddress:   report.IPAddress,
					ConnectedAt: time.Now(),
					LastSeen:    time.Now(),
				}
				if err := s.db.Create(&onlineUser).Error; err != nil {
					continue
				}
			}
		} else {
			// 更新现有记录
			onlineUser.LastSeen = time.Now()
			if err := s.db.Save(&onlineUser).Error; err != nil {
				continue
			}
		}
	}

	return nil
}

// 获取在线统计
func (s *V2bXService) getAliveList(node *model.Node) (*AliveListResponse, error) {
	// 获取该节点的在线用户
	var onlineUsers []model.OnlineUser
	if err := s.db.Where("node_id = ?", node.ID).Find(&onlineUsers).Error; err != nil {
		return nil, err
	}

	// 构建响应
	userList := make([]string, 0, len(onlineUsers))
	for _, onlineUser := range onlineUsers {
		// 获取用户UUID
		var user model.User
		if err := s.db.First(&user, onlineUser.UserID).Error; err == nil {
			userList = append(userList, user.UUID)
		}
	}

	return &AliveListResponse{
		Users: userList,
		Count: len(userList),
	}, nil
}

// 检查用户设备限制
func (s *V2bXService) checkDeviceLimit(userID uint, nodeID uint, currentIP string) bool {
	// 统计用户在该节点的连接数
	var connectionCount int64
	if err := s.db.Model(&model.OnlineUser{}).
		Where("user_id = ? AND node_id = ?", userID, nodeID).
		Count(&connectionCount).Error; err != nil {
		return false
	}

	// 获取用户的设备限制
	user, err := s.userService.GetUserByID(userID)
	if err != nil {
		return false
	}

	// 如果设备限制为0，表示无限制
	if user.DeviceLimit == 0 {
		return true
	}

	// 检查是否超过设备限制
	return connectionCount < int64(user.DeviceLimit)
}

// 获取节点统计信息
func (s *V2bXService) GetNodeStats(nodeID uint) (map[string]interface{}, error) {
	// 获取节点信息
	node, err := s.nodeService.GetNodeByID(nodeID)
	if err != nil {
		return nil, err
	}

	// 获取在线用户数
	var onlineCount int64
	if err := s.db.Model(&model.OnlineUser{}).
		Where("node_id = ?", nodeID).
		Count(&onlineCount).Error; err != nil {
		return nil, err
	}

	// 获取今日流量统计
	var todayTraffic int64
	if err := s.db.Model(&model.TrafficLog{}).
		Where("node_id = ? AND DATE(recorded_at) = CURDATE()", nodeID).
		Select("COALESCE(SUM(upload + download), 0)").
		Scan(&todayTraffic).Error; err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"node_id":        node.ID,
		"node_name":      node.Name,
		"node_status":    node.Status,
		"online_users":   onlineCount,
		"today_traffic":  todayTraffic,
		"max_users":      node.MaxUsers,
		"traffic_rate":   node.TrafficRate,
	}

	return stats, nil
}

// getPermissionGroupIDs 获取权限组ID列表
func getPermissionGroupIDs(groups []model.PermissionGroup) []uint {
	ids := make([]uint, len(groups))
	for i, group := range groups {
		ids[i] = group.ID
	}
	return ids
}

// hasNodeAccess 检查用户是否有权限访问节点
func (s *V2bXService) hasNodeAccess(user *model.User, permissionGroups []model.PermissionGroup) bool {
	// 检查用户是否有活跃订阅
	var activeSubscription model.UserSubscription
	if err := s.db.Joins("JOIN products ON products.id = user_subscriptions.product_id").
		Where("user_subscriptions.user_id = ? AND user_subscriptions.status = ? AND user_subscriptions.expired_at > ? AND products.permission_group_id IN ?", 
			user.ID, "active", time.Now(), getPermissionGroupIDs(permissionGroups)).
		First(&activeSubscription).Error; err != nil {
		return false
	}

	return true
}

// 清理过期的在线用户记录
func (s *V2bXService) CleanupExpiredOnlineUsers() error {
	// 清理超过10分钟未更新的在线记录
	cutoffTime := time.Now().Add(-10 * time.Minute)
	if err := s.db.Where("last_seen < ?", cutoffTime).
		Delete(&model.OnlineUser{}).Error; err != nil {
		return fmt.Errorf("清理过期在线用户失败: %v", err)
	}

	return nil
}

// getNodeByIDAndType 根据ID和类型获取节点
func (s *V2bXService) getNodeByIDAndType(nodeID uint, nodeType string) (*model.Node, error) {
	// 标准化节点类型（类似v2board的处理）
	normalizedType := s.normalizeNodeType(nodeType)
	
	var node model.Node
	if err := s.db.Where("id = ? AND protocol = ?", nodeID, normalizedType).First(&node).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("节点不存在或类型不匹配")
		}
		return nil, err
	}

	return &node, nil
}

// normalizeNodeType 标准化节点类型名称
func (s *V2bXService) normalizeNodeType(nodeType string) string {
	// 类似v2board的类型映射
	switch nodeType {
	case "v2ray":
		return "vmess"
	case "hysteria2":
		return "hysteria"
	case "shadowsock":
		return "shadowsocks" // 确保单数形式
	default:
		return nodeType
	}
}

// generateNodeConfigByType 根据节点类型生成配置
func (s *V2bXService) generateNodeConfigByType(node *model.Node, nodeType string) (map[string]interface{}, error) {
	// 标准化节点类型
	normalizedType := s.normalizeNodeType(nodeType)
	
	// 基础配置
	baseConfig := map[string]interface{}{
		"push_interval": 60,
		"pull_interval": 60,
	}

	// 根据节点类型生成特定配置
	switch normalizedType {
	case "shadowsocks":
		return s.generateShadowsocksConfig(node, baseConfig)
	case "vmess":
		return s.generateVmessConfig(node, baseConfig)
	case "vless":
		return s.generateVlessConfig(node, baseConfig)
	case "trojan":
		return s.generateTrojanConfig(node, baseConfig)
	case "tuic":
		return s.generateTuicConfig(node, baseConfig)
	case "hysteria":
		return s.generateHysteriaConfig(node, baseConfig)
	case "anytls":
		return s.generateAnyTLSConfig(node, baseConfig)
	default:
		return nil, fmt.Errorf("不支持的节点类型: %s", normalizedType)
	}
}

// 各种协议的配置生成方法
func (s *V2bXService) generateShadowsocksConfig(node *model.Node, baseConfig map[string]interface{}) (map[string]interface{}, error) {
	// 解析Shadowsocks特定配置
	var ssConfig struct {
		Cipher          string `json:"cipher"`
		Obfs            string `json:"obfs"`
		ObfsSettings    map[string]interface{} `json:"obfs_settings"`
	}
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &ssConfig); err != nil {
			return nil, fmt.Errorf("解析Shadowsocks配置失败: %v", err)
		}
	}

	config := map[string]interface{}{
		"server_port":    node.Port,
		"cipher":         ssConfig.Cipher,
		"obfs":           ssConfig.Obfs,
		"obfs_settings":  ssConfig.ObfsSettings,
		"base_config":    baseConfig,
	}

	return config, nil
}

func (s *V2bXService) generateVmessConfig(node *model.Node, baseConfig map[string]interface{}) (map[string]interface{}, error) {
	// 解析Vmess特定配置
	var vmessConfig struct {
		Network         string `json:"network"`
		NetworkSettings map[string]interface{} `json:"networkSettings"`
		TLS             bool   `json:"tls"`
	}
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &vmessConfig); err != nil {
			return nil, fmt.Errorf("解析Vmess配置失败: %v", err)
		}
	}

	config := map[string]interface{}{
		"server_port":      node.Port,
		"network":          vmessConfig.Network,
		"networkSettings": vmessConfig.NetworkSettings,
		"tls":             vmessConfig.TLS,
		"base_config":     baseConfig,
	}

	return config, nil
}

func (s *V2bXService) generateVlessConfig(node *model.Node, baseConfig map[string]interface{}) (map[string]interface{}, error) {
	// 解析Vless特定配置
	var vlessConfig struct {
		Network         string `json:"network"`
		NetworkSettings map[string]interface{} `json:"network_settings"`
		TLS             bool   `json:"tls"`
		Flow            string `json:"flow"`
		TLSSettings    map[string]interface{} `json:"tls_settings"`
	}
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &vlessConfig); err != nil {
			return nil, fmt.Errorf("解析Vless配置失败: %v", err)
		}
	}

	config := map[string]interface{}{
		"server_port":      node.Port,
		"network":          vlessConfig.Network,
		"networkSettings": vlessConfig.NetworkSettings,
		"tls":             vlessConfig.TLS,
		"flow":            vlessConfig.Flow,
		"tls_settings":    vlessConfig.TLSSettings,
		"base_config":     baseConfig,
	}

	return config, nil
}

func (s *V2bXService) generateTrojanConfig(node *model.Node, baseConfig map[string]interface{}) (map[string]interface{}, error) {
	// 解析Trojan特定配置
	var trojanConfig struct {
		Network         string `json:"network"`
		NetworkSettings map[string]interface{} `json:"network_settings"`
		ServerName      string `json:"server_name"`
	}
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &trojanConfig); err != nil {
			return nil, fmt.Errorf("解析Trojan配置失败: %v", err)
		}
	}

	config := map[string]interface{}{
		"host":            node.Host,
		"server_port":    node.Port,
		"network":        trojanConfig.Network,
		"networkSettings": trojanConfig.NetworkSettings,
		"server_name":    trojanConfig.ServerName,
		"base_config":    baseConfig,
	}

	return config, nil
}

func (s *V2bXService) generateTuicConfig(node *model.Node, baseConfig map[string]interface{}) (map[string]interface{}, error) {
	// 解析Tuic特定配置
	var tuicConfig struct {
		ServerName        string `json:"server_name"`
		CongestionControl string `json:"congestion_control"`
		ZeroRTTHandshake   bool   `json:"zero_rtt_handshake"`
	}
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &tuicConfig); err != nil {
			return nil, fmt.Errorf("解析Tuic配置失败: %v", err)
		}
	}

	config := map[string]interface{}{
		"server_port":         node.Port,
		"server_name":         tuicConfig.ServerName,
		"congestion_control":  tuicConfig.CongestionControl,
		"zero_rtt_handshake":  tuicConfig.ZeroRTTHandshake,
		"base_config":         baseConfig,
	}

	return config, nil
}

func (s *V2bXService) generateHysteriaConfig(node *model.Node, baseConfig map[string]interface{}) (map[string]interface{}, error) {
	// 解析Hysteria特定配置
	var hysteriaConfig struct {
		Version     int    `json:"version"`
		Host        string `json:"host"`
		ServerName  string `json:"server_name"`
		UpMbps      int    `json:"up_mbps"`
		DownMbps    int    `json:"down_mbps"`
		Obfs        string `json:"obfs"`
		ObfsPassword string `json:"obfs_password"`
	}
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &hysteriaConfig); err != nil {
			return nil, fmt.Errorf("解析Hysteria配置失败: %v", err)
		}
	}

	config := map[string]interface{}{
		"version":      hysteriaConfig.Version,
		"host":         hysteriaConfig.Host,
		"server_port":  node.Port,
		"server_name":  hysteriaConfig.ServerName,
		"up_mbps":      hysteriaConfig.UpMbps,
		"down_mbps":    hysteriaConfig.DownMbps,
		"base_config":  baseConfig,
	}

	// 根据版本添加不同字段
	if hysteriaConfig.Version == 1 {
		config["obfs"] = hysteriaConfig.Obfs
	} else if hysteriaConfig.Version == 2 {
		if hysteriaConfig.UpMbps == 0 && hysteriaConfig.DownMbps == 0 {
			config["ignore_client_bandwidth"] = true
		} else {
			config["ignore_client_bandwidth"] = false
		}
		config["obfs"] = hysteriaConfig.Obfs
		config["obfs-password"] = hysteriaConfig.ObfsPassword
	}

	return config, nil
}

func (s *V2bXService) generateAnyTLSConfig(node *model.Node, baseConfig map[string]interface{}) (map[string]interface{}, error) {
	// 解析AnyTLS特定配置
	var anytlsConfig struct {
		PaddingScheme []string `json:"padding_scheme"`
	}
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &anytlsConfig); err != nil {
			return nil, fmt.Errorf("解析AnyTLS配置失败: %v", err)
		}
	}

	config := map[string]interface{}{
		"server_port":    node.Port,
		"server_name":    node.ServerName,
		"padding_scheme": anytlsConfig.PaddingScheme,
		"base_config":    baseConfig,
	}

	return config, nil
}

// 中间件：V2bX节点认证
func V2bXAuthMiddleware(s *V2bXService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从查询参数或Header中获取token
		token := c.Query("token")
		if token == "" {
			token = c.GetHeader("X-Node-Token")
		}
		if token == "" {
			token = c.GetHeader("Authorization")
			// 处理Bearer token格式
			if len(token) > 7 && token[:7] == "Bearer " {
				token = token[7:]
			}
		}

		// 验证token
		node, err := s.validateNodeToken(token)
		if err != nil {
			c.JSON(401, gin.H{
				"success": false,
				"message": "认证失败",
				"error":   err.Error(),
			})
			c.Abort()
			return
		}

		// 将节点信息存储在上下文中
		c.Set("node", node)
		c.Set("node_id", node.ID)
		c.Next()
	}
}