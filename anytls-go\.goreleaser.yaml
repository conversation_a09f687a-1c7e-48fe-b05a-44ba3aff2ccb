# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
project_name: anytls
builds:
  - id: anytls-client
    binary: anytls-client
    dir: cmd/client
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    goarch:
      - amd64
      - arm64
    flags:
      - -trimpath
      - -buildvcs=false
    ldflags:
      - -s -w
  - id: anytls-server
    binary: anytls-server
    dir: cmd/server
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    goarch:
      - amd64
      - arm64
    flags:
      - -trimpath
      - -buildvcs=false
    ldflags:
      - -s -w
archives:
  - id: anytls
    builds:
      - anytls-client
      - anytls-server
    name_template: "{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    format: zip
