package service

import (
	"anypanel/internal/model"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type TrafficService struct {
	db           *gorm.DB
	userService  *UserService
	nodeService  *NodeService
	v2bxService  *V2bXService
}

func NewTrafficService(db *gorm.DB, userService *UserService, nodeService *NodeService, v2bxService *V2bXService) *TrafficService {
	return &TrafficService{
		db:          db,
		userService: userService,
		nodeService: nodeService,
		v2bxService: v2bxService,
	}
}

// TrafficStats 流量统计结构
type TrafficStats struct {
	UserID      uint      `json:"user_id"`
	NodeID      uint      `json:"node_id"`
	Upload      int64     `json:"upload"`
	Download    int64     `json:"download"`
	Total       int64     `json:"total"`
	RecordedAt  time.Time `json:"recorded_at"`
}

// UserTrafficSummary 用户流量汇总
type UserTrafficSummary struct {
	UserID        uint   `json:"user_id"`
	Username      string `json:"username"`
	Email         string `json:"email"`
	TrafficUsed   int64  `json:"traffic_used"`
	TrafficLimit  int64  `json:"traffic_limit"`
	UsagePercent  float64 `json:"usage_percent"`
	Status        string `json:"status"`
}

// NodeTrafficSummary 节点流量汇总
type NodeTrafficSummary struct {
	NodeID        uint   `json:"node_id"`
	NodeName      string `json:"node_name"`
	Upload        int64  `json:"upload"`
	Download      int64  `json:"download"`
	Total         int64  `json:"total"`
	OnlineUsers   int64  `json:"online_users"`
	Status        string `json:"status"`
}

// TrafficReportRequest 流量报表请求
type TrafficReportRequest struct {
	StartTime    time.Time `json:"start_time" form:"start_time"`
	EndTime      time.Time `json:"end_time" form:"end_time"`
	UserID       *uint     `json:"user_id" form:"user_id"`
	NodeID       *uint     `json:"node_id" form:"node_id"`
	GroupBy      string    `json:"group_by" form:"group_by"` // day, week, month
}

// TrafficReportResponse 流量报表响应
type TrafficReportResponse struct {
	TotalUpload   int64           `json:"total_upload"`
	TotalDownload int64           `json:"total_download"`
	TotalTraffic  int64           `json:"total_traffic"`
	Records       []TrafficStats  `json:"records"`
	Summary       map[string]int64 `json:"summary"`
}

// ProcessV2bXTraffic 处理V2bX上报的流量数据
func (s *TrafficService) ProcessV2bXTraffic(nodeToken string, reports []TrafficReport) error {
	return s.v2bxService.ReportTraffic(nodeToken, reports)
}

// GetTrafficStats 获取流量统计
func (s *TrafficService) GetTrafficStats(req *TrafficReportRequest) (*TrafficReportResponse, error) {
	query := s.db.Model(&model.TrafficLog{})

	// 时间范围筛选
	if !req.StartTime.IsZero() {
		query = query.Where("recorded_at >= ?", req.StartTime)
	}
	if !req.EndTime.IsZero() {
		query = query.Where("recorded_at <= ?", req.EndTime)
	}

	// 用户筛选
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}

	// 节点筛选
	if req.NodeID != nil {
		query = query.Where("node_id = ?", *req.NodeID)
	}

	// 获取总数统计
	var stats struct {
		TotalUpload   int64
		TotalDownload int64
	}
	
	if err := query.Select(
		"COALESCE(SUM(upload), 0) as total_upload",
		"COALESCE(SUM(download), 0) as total_download",
	).Scan(&stats).Error; err != nil {
		return nil, err
	}

	// 获取详细记录
	var records []model.TrafficLog
	if err := query.Order("recorded_at DESC").Find(&records).Error; err != nil {
		return nil, err
	}

	// 转换为TrafficStats
	trafficStats := make([]TrafficStats, len(records))
	for i, record := range records {
		trafficStats[i] = TrafficStats{
			UserID:     record.UserID,
			NodeID:     record.NodeID,
			Upload:     record.Upload,
			Download:   record.Download,
			Total:      record.Upload + record.Download,
			RecordedAt: record.RecordedAt,
		}
	}

	// 按时间分组统计
	summary := make(map[string]int64)
	if req.GroupBy != "" {
		groupedStats, err := s.getGroupedTrafficStats(req)
		if err != nil {
			return nil, err
		}
		summary = groupedStats
	}

	response := &TrafficReportResponse{
		TotalUpload:   stats.TotalUpload,
		TotalDownload: stats.TotalDownload,
		TotalTraffic:  stats.TotalUpload + stats.TotalDownload,
		Records:       trafficStats,
		Summary:       summary,
	}

	return response, nil
}

// GetTrafficTrends 获取流量趋势
func (s *TrafficService) GetTrafficTrends(days int) (map[string]interface{}, error) {
	if days <= 0 {
		days = 7 // 默认7天
	}

	startTime := time.Now().AddDate(0, 0, -days)
	
	// 按天统计流量
	var dailyStats []struct {
		Date        string `json:"date"`
		Upload      int64  `json:"upload"`
		Download    int64  `json:"download"`
		Total       int64  `json:"total"`
		ActiveUsers int64  `json:"active_users"`
	}

	if err := s.db.Model(&model.TrafficLog{}).
		Select("DATE(recorded_at) as date, SUM(upload) as upload, SUM(download) as download, COUNT(DISTINCT user_id) as active_users").
		Where("recorded_at >= ?", startTime).
		Group("DATE(recorded_at)").
		Order("date ASC").
		Scan(&dailyStats).Error; err != nil {
		return nil, err
	}

	// 计算趋势数据
	totalUpload := int64(0)
	totalDownload := int64(0)
	for _, stat := range dailyStats {
		totalUpload += stat.Upload
		totalDownload += stat.Download
	}

	// 获取用户总数
	var totalUsers int64
	if err := s.db.Model(&model.User{}).
		Where("status = ?", "active").
		Count(&totalUsers).Error; err != nil {
		return nil, err
	}

	// 获取在线用户数
	var onlineUsers int64
	if err := s.db.Model(&model.OnlineUser{}).
		Where("last_seen >= ?", time.Now().Add(-5*time.Minute)).
		Count(&onlineUsers).Error; err != nil {
		return nil, err
	}

	trends := map[string]interface{}{
		"period":        fmt.Sprintf("%d天", days),
		"total_upload":  totalUpload,
		"total_download": totalDownload,
		"total_traffic": totalUpload + totalDownload,
		"total_users":   totalUsers,
		"online_users":  onlineUsers,
		"daily_stats":   dailyStats,
		"avg_daily_traffic": (totalUpload + totalDownload) / int64(days),
	}

	return trends, nil
}

// GetUserTrafficSummary 获取用户流量汇总
func (s *TrafficService) GetUserTrafficSummary() ([]UserTrafficSummary, error) {
	var users []model.User
	if err := s.db.Find(&users).Error; err != nil {
		return nil, err
	}

	summaries := make([]UserTrafficSummary, 0, len(users))
	for _, user := range users {
		// 计算使用率
		usagePercent := 0.0
		if user.TrafficLimit > 0 {
			usagePercent = float64(user.TrafficUsed) / float64(user.TrafficLimit) * 100
		}

		summary := UserTrafficSummary{
			UserID:       user.ID,
			Username:     user.Username,
			Email:        user.Email,
			TrafficUsed:  user.TrafficUsed,
			TrafficLimit: user.TrafficLimit,
			UsagePercent: usagePercent,
			Status:       user.Status,
		}
		summaries = append(summaries, summary)
	}

	return summaries, nil
}

// GetNodeTrafficSummary 获取节点流量汇总
func (s *TrafficService) GetNodeTrafficSummary() ([]NodeTrafficSummary, error) {
	var nodes []model.Node
	if err := s.db.Find(&nodes).Error; err != nil {
		return nil, err
	}

	summaries := make([]NodeTrafficSummary, 0, len(nodes))
	for _, node := range nodes {
		// 获取节点流量统计
		var stats struct {
			Upload   int64
			Download int64
		}
		
		if err := s.db.Model(&model.TrafficLog{}).
			Select("COALESCE(SUM(upload), 0) as upload, COALESCE(SUM(download), 0) as download").
			Where("node_id = ?", node.ID).
			Scan(&stats).Error; err != nil {
			continue
		}

		// 获取在线用户数
		var onlineUsers int64
		if err := s.db.Model(&model.OnlineUser{}).
			Where("node_id = ? AND last_seen >= ?", node.ID, time.Now().Add(-5*time.Minute)).
			Count(&onlineUsers).Error; err != nil {
			onlineUsers = 0
		}

		summary := NodeTrafficSummary{
			NodeID:      node.ID,
			NodeName:    node.Name,
			Upload:      stats.Upload,
			Download:    stats.Download,
			Total:       stats.Upload + stats.Download,
			OnlineUsers: onlineUsers,
			Status:      node.Status,
		}
		summaries = append(summaries, summary)
	}

	return summaries, nil
}

// CheckTrafficLimits 检查流量限制并更新用户状态
func (s *TrafficService) CheckTrafficLimits() error {
	// 获取所有活跃用户
	users, err := s.userService.GetActiveUsers()
	if err != nil {
		return err
	}

	updatedCount := 0
	for _, user := range users {
		// 检查流量限制
		if user.TrafficLimit > 0 && user.TrafficUsed >= user.TrafficLimit {
			// 更新用户状态为过期
			if err := s.userService.UpdateUserStatus(user.ID, "expired"); err != nil {
				continue
			}
			updatedCount++
		}
	}

	return nil
}

// CleanupOldTrafficLogs 清理旧的流量日志
func (s *TrafficService) CleanupOldTrafficLogs(days int) error {
	if days <= 0 {
		days = 90 // 默认保留90天
	}

	cutoffTime := time.Now().AddDate(0, 0, -days)
	
	// 删除旧日志
	if err := s.db.Where("recorded_at < ?", cutoffTime).
		Delete(&model.TrafficLog{}).Error; err != nil {
		return err
	}

	return nil
}

// ExportTrafficReport 导出流量报表
func (s *TrafficService) ExportTrafficReport(req *TrafficReportRequest) ([][]string, error) {
	// 获取流量数据
	report, err := s.GetTrafficStats(req)
	if err != nil {
		return nil, err
	}

	// 构建CSV数据
	var csvData [][]string
	
	// 添加表头
	headers := []string{
		"用户ID",
		"节点ID", 
		"上传流量(字节)",
		"下载流量(字节)",
		"总流量(字节)",
		"记录时间",
	}
	csvData = append(csvData, headers)

	// 添加数据行
	for _, record := range report.Records {
		row := []string{
			fmt.Sprintf("%d", record.UserID),
			fmt.Sprintf("%d", record.NodeID),
			fmt.Sprintf("%d", record.Upload),
			fmt.Sprintf("%d", record.Download),
			fmt.Sprintf("%d", record.Total),
			record.RecordedAt.Format("2006-01-02 15:04:05"),
		}
		csvData = append(csvData, row)
	}

	// 添加汇总行
	summaryRow := []string{
		"汇总",
		"",
		fmt.Sprintf("%d", report.TotalUpload),
		fmt.Sprintf("%d", report.TotalDownload),
		fmt.Sprintf("%d", report.TotalTraffic),
		"",
	}
	csvData = append(csvData, summaryRow)

	return csvData, nil
}

// getGroupedTrafficStats 获取分组统计
func (s *TrafficService) getGroupedTrafficStats(req *TrafficReportRequest) (map[string]int64, error) {
	query := s.db.Model(&model.TrafficLog{})

	// 应用筛选条件
	if !req.StartTime.IsZero() {
		query = query.Where("recorded_at >= ?", req.StartTime)
	}
	if !req.EndTime.IsZero() {
		query = query.Where("recorded_at <= ?", req.EndTime)
	}
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}
	if req.NodeID != nil {
		query = query.Where("node_id = ?", *req.NodeID)
	}

	// 根据分组类型进行统计
	var results []struct {
		Group string `json:"group"`
		Total int64  `json:"total"`
	}

	switch req.GroupBy {
	case "day":
		query = query.Select("DATE(recorded_at) as group, SUM(upload + download) as total").
			Group("DATE(recorded_at)")
	case "week":
		query = query.Select("YEARWEEK(recorded_at) as group, SUM(upload + download) as total").
			Group("YEARWEEK(recorded_at)")
	case "month":
		query = query.Select("DATE_FORMAT(recorded_at, '%Y-%m') as group, SUM(upload + download) as total").
			Group("DATE_FORMAT(recorded_at, '%Y-%m')")
	default:
		return make(map[string]int64), nil
	}

	if err := query.Scan(&results).Error; err != nil {
		return nil, err
	}

	// 构建分组统计
	summary := make(map[string]int64)
	for _, result := range results {
		summary[result.Group] = result.Total
	}

	return summary, nil
}

// GetRealtimeTrafficStats 获取实时流量统计
func (s *TrafficService) GetRealtimeTrafficStats() (map[string]interface{}, error) {
	// 获取最近5分钟的流量统计
	recentTime := time.Now().Add(-5 * time.Minute)
	
	var recentStats struct {
		Upload   int64
		Download int64
		Count    int64
	}

	if err := s.db.Model(&model.TrafficLog{}).
		Select("COALESCE(SUM(upload), 0) as upload, COALESCE(SUM(download), 0) as download, COUNT(*) as count").
		Where("recorded_at >= ?", recentTime).
		Scan(&recentStats).Error; err != nil {
		return nil, err
	}

	// 获取今日流量统计
	var todayStats struct {
		Upload   int64
		Download int64
	}

	if err := s.db.Model(&model.TrafficLog{}).
		Select("COALESCE(SUM(upload), 0) as upload, COALESCE(SUM(download), 0) as download").
		Where("DATE(recorded_at) = CURDATE()").
		Scan(&todayStats).Error; err != nil {
		return nil, err
	}

	// 获取在线用户数
	var onlineUsers int64
	if err := s.db.Model(&model.OnlineUser{}).
		Where("last_seen >= ?", time.Now().Add(-5*time.Minute)).
		Count(&onlineUsers).Error; err != nil {
		return nil, err
	}

	// 获取活跃节点数
	var activeNodes int64
	if err := s.db.Model(&model.Node{}).
		Where("status = ?", "online").
		Count(&activeNodes).Error; err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"recent_traffic": map[string]int64{
			"upload":   recentStats.Upload,
			"download": recentStats.Download,
			"total":    recentStats.Upload + recentStats.Download,
			"requests": recentStats.Count,
		},
		"today_traffic": map[string]int64{
			"upload":   todayStats.Upload,
			"download": todayStats.Download,
			"total":    todayStats.Upload + todayStats.Download,
		},
		"system_stats": map[string]int64{
			"online_users":  onlineUsers,
			"active_nodes":  activeNodes,
		},
		"updated_at": time.Now().Format("2006-01-02 15:04:05"),
	}

	return stats, nil
}