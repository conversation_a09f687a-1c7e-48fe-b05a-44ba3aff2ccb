package model

import (
	"gorm.io/gorm"
)

// AutoMigrate 自动迁移数据库表
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&User{},
		&Node{},
		&PermissionGroup{},
		&Product{},
		&UserSubscription{},
		&Order{},
		&TrafficLog{},
		&OnlineUser{},
		&Payment{},
		&PaymentLog{},
	)
}

// CreateIndexes 创建数据库索引
func CreateIndexes(db *gorm.DB) error {
	// 用户相关索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_users_expired_at ON users(expired_at)").Error; err != nil {
		return err
	}

	// 节点相关索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_nodes_protocol ON nodes(protocol)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_nodes_status ON nodes(status)").Error; err != nil {
		return err
	}

	// 订单相关索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_orders_user_status ON orders(user_id, status)").Error; err != nil {
		return err
	}

	// 订阅相关索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_status ON user_subscriptions(user_id, status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_user_subscriptions_expired_at ON user_subscriptions(expired_at)").Error; err != nil {
		return err
	}

	// 流量统计相关索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_traffic_logs_user_time ON traffic_logs(user_id, recorded_at)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_traffic_logs_node_time ON traffic_logs(node_id, recorded_at)").Error; err != nil {
		return err
	}

	// 在线用户相关索引
	if err := db.Exec("CREATE UNIQUE INDEX IF NOT EXISTS idx_online_users_unique ON online_users(user_id, node_id, ip_address)").Error; err != nil {
		return err
	}

	// 支付方式相关索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_payments_method ON payments(method)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_payments_enable ON payments(enable)").Error; err != nil {
		return err
	}

	// 支付日志相关索引
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_payment_logs_order_id ON payment_logs(order_id)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_payment_logs_method ON payment_logs(method)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_payment_logs_status ON payment_logs(status)").Error; err != nil {
		return err
	}
	if err := db.Exec("CREATE INDEX IF NOT EXISTS idx_payment_logs_created_at ON payment_logs(created_at)").Error; err != nil {
		return err
	}

	return nil
}