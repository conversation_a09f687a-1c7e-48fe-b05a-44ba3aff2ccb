# 用户常见疑问

## ERR_CONNECTION_CLOSED / 代理关闭且没有日志

常见原因：

- 密码错误，请检查您的密码是否正确。

## 好慢

网络速度与线路质量有关，升级更优质的线路可以提升速度。

## 为什么选项这么少 / 为什么是自签证书

本项目只是提供一个简洁的 Any in TLS 代理的示例，并不旨在成为“通用代理工具”。

作为参考实现，不对 TLS 协议本身做过多的处理。

当然，如果你把这个协议集成到某些代理平台中，你将能够更好地控制 TLS ClientHello/ServerHello。

## FingerPrint 之类的选项呢

TLS 本身（ClientHello/ServerHello）的特征不是本项目关注的重点，现有的工具很容易改变这些特征。

- 某些 Golang 灵车代理早已标配 uTLS，为什么还被墙？
- 某些 Golang 灵车代理即使不用 uTLS，直接使用 Golang TLS 栈，为什么不被墙？

## 关于默认的 PaddingScheme

默认 PaddingScheme 只是一个示例。本项目无法确保默认参数不会被墙，因此设计了更新参数改变流量特征的机制。我们相信，实现成本低廉且易于改变的流量特征更有可能达到阻碍审查研究的目的。

## 如何更改 PaddingScheme

服务器设置 `--padding-scheme ./padding.txt` 参数。

## 还有别的 PaddingScheme 吗

模拟 XTLS-Vision:

```
stop=3
0=900-1400
1=900-1400
2=900-1400
```

模仿的不是特别像，但可以说明 XTLS-Vision 的弊端：写死的长度处理逻辑，只要 GFW 更新特征库就能识别。

## 命名疑问 / 更换传输层

事实上，如果您愿意，您可以将协议放置在其他传输加密层上，这需要一些代码，但不太多。

本协议主要负责的工作：

1. 合理的 TCP 连接复用与性能表现 (`proxy/session`)
2. 控制数据包长度模式，缓解“嵌套的TLS握手指纹识别” (`proxy/session` `proxy/padding`)

但是仅完成以上工作，仍然无法提供一个“好用”的代理。其他不得不完成的工作，例如加密，目前是依赖 TLS 完成的，因此协议取名为 AnyTLS。

更换其他传输层，您可能会失去 TLS 提供的安全保护。如果用于翻墙，还可能会触发不同的防火墙规则。

**除了“过 CDN”或“牺牲安全性来减少延迟”外，我想不出更换传输层的理由。如果你想尝试，请自行承担风险。**

## 参考过的项目

https://github.com/xtaci/smux

https://github.com/3andne/restls

https://github.com/SagerNet/sing-box

https://github.com/klzgrad/naiveproxy

## 已知弱点

以下弱点目前可能不会轻易引发“被墙”（甚至可能在大规模 DPI 下很难被利用），且修复可能引发协议不兼容，因此 anytls v1 没有对这些弱点进行处理。

- TLS over TLS 需要比普通 h2 请求更多的握手往返，也就是说，没有 h2 请求需要这么多的来回握手。除了进行 MITM 代理、破坏 E2E 加密之外，没有简单的方法可以避免这种情况。
- anytls 没有处理下行流量。虽然处理下行包特征会损失性能，但总的来说这一点很容易修复且不会破坏兼容性。
- anytls 现有的 PaddingScheme 语法对单个包的长度指定只有“单一固定长度”和“单一范围内随机”两种模式。此外 PaddingScheme 处理完毕后的剩余数据也只能直接发送。要修复这点，需要重新设计一套更复杂的 PaddingScheme 语法。
- anytls 几乎同时地发送三个或更多的数据包，特别是在 TLS 握手后的第一个 RTT 之内。即使单个数据包的长度符合某种被豁免的特征，也仍有可能被用于 `到达时间 - 包长 - 包数量` 和 `到达时间 - 通信数据量` 等统计。要修复这点，需要设置更多的缓冲区，实现将 auth 和 cmdSettings 等包合并发送，这会破坏现有 PaddingScheme 的语义。
- 即使修复了上一条所述的问题，包计数器仍然不一定能代表发包的时机，因为不可能预测被代理方的发送时机。
- 目前不清楚客户端初始化时使用默认 PaddingScheme 发起的少量连接，以及某些机械性的测试连接是否会对整体统计造成影响？
- 目前不清楚 GFW 对 TLS 连接会持续跟踪多久。
- TLS over TLS 开销导致可见的数据包长度增大和小数据包的缺失。消除这种开销还需要 MITM 代理。
- TLS over TLS 开销还会导致数据包持续超过 MTU 限制，这对于原始用户代理来说不应该发生。
- 由于这不是 HTTP 服务器，仍然可能存在主动探测问题，即使 gfw 对翻墙协议的主动探测似乎已不常见。
