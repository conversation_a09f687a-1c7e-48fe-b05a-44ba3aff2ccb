package api

import (
	"anypanel/internal/config"
	"anypanel/internal/middleware"
	"anypanel/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes 设置所有路由
func SetupRoutes(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.RateLimitMiddleware())
	r.Use(middleware.RequestIDMiddleware())
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.RecoveryMiddleware())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"message": "AnyPanel API is running",
		})
	})

	// API v1 路由组
	v1 := r.Group("/api/v1")
	{
		// 认证路由
		SetupAuthRoutes(v1, db, cfg)
		
		// 用户路由
		SetupUserRoutes(v1, db)
		
		// 权限组路由
		SetupPermissionGroupRoutes(v1, db)
		
		// 节点路由
		SetupNodeRoutes(v1, db)
		
		// 商品系统路由
		SetupProductRoutes(v1, db)
		
		// V2bX兼容API路由
		SetupV2bXRoutes(v1, db, cfg)
		
		// 支付路由
		SetupPaymentRoutes(v1, db)
	}

	return r
}

// SetupAdminRoutes 设置管理员路由
func SetupAdminRoutes(router *gin.RouterGroup, db *gorm.DB) {
	userService := service.NewUserService(db)
	nodeService := service.NewNodeService(db)
	permissionGroupService := service.NewPermissionGroupService(db)
	productService := service.NewProductService(db)
	orderService := service.NewOrderService(db)
	trafficService := service.NewTrafficService(db, nil, nil, nil)
	onlineUserService := service.NewOnlineUserService(db, nil, nil, nil)

	adminAPI := &AdminAPI{
		userService:            userService,
		nodeService:            nodeService,
		permissionGroupService:  permissionGroupService,
		productService:         productService,
		orderService:            orderService,
		trafficService:          trafficService,
		onlineUserService:       onlineUserService,
	}

	admin := router.Group("/admin")
	admin.Use(middleware.AuthMiddleware(db))
	admin.Use(middleware.AdminMiddleware())
	{
		// 系统管理路由
		system := admin.Group("/system")
		{
			system.GET("/stats", adminAPI.GetSystemStats)
			system.POST("/cleanup-task", adminAPI.RunCleanupTask)
			system.GET("/traffic-stats", adminAPI.GetTrafficSystemStats)
			system.GET("/online-user-stats", adminAPI.GetOnlineUserStats)
			system.POST("/optimize", adminAPI.OptimizeSystem)
		}
		
		// 管理员仪表板
		admin.GET("/dashboard", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "管理员仪表板",
			})
		})
		
		// 系统设置
		admin.GET("/settings", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "系统设置",
			})
		})
		
		// 系统日志
		admin.GET("/logs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "系统日志",
			})
		})
	}
}

// SetupPaymentRoutes 设置支付路由
func SetupPaymentRoutes(router *gin.RouterGroup, db *gorm.DB) {
	paymentService := service.NewPaymentService(db)
	orderService := service.NewOrderService(db)
	paymentAPI := NewPaymentAPI(paymentService, orderService)

	// 支付方式管理路由
	payment := router.Group("/payment")
	{
		// 支付方式列表
		payment.GET("/methods", paymentAPI.GetPaymentMethods)
		
		// 支付方式详情
		payment.GET("/methods/:method", paymentAPI.GetPaymentMethod)
		
		// 支付方式配置表单
		payment.GET("/methods/:method/form", paymentAPI.GetPaymentForm)
		
		// 创建支付方式
		payment.POST("/methods", paymentAPI.CreatePaymentMethod)
		
		// 更新支付方式
		payment.PUT("/methods/:id", paymentAPI.UpdatePaymentMethod)
		
		// 删除支付方式
		payment.DELETE("/methods/:id", paymentAPI.DeletePaymentMethod)
		
		// 创建支付订单
		payment.POST("/create", paymentAPI.CreatePayment)
		
		// 支付回调
		payment.POST("/notify/:method/:uuid", paymentAPI.PaymentNotify)
		
		// 支付日志
		payment.GET("/logs", paymentAPI.GetPaymentLogs)
		
		// 支付统计
		payment.GET("/stats", paymentAPI.GetPaymentStats)
		
		// 退款处理
		payment.POST("/refund", paymentAPI.ProcessRefund)
	}

	// 游客支付路由（无需认证）
	guest := router.Group("/guest")
	{
		// 支付回调
		guest.POST("/payment/notify/:method/:uuid", paymentAPI.PaymentNotify)
	}
}