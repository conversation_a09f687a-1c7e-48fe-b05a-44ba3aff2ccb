package api

import (
	"anypanel/internal/config"
	"anypanel/internal/middleware"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes 设置所有路由
func SetupRoutes(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.RateLimitMiddleware())
	r.Use(middleware.RequestIDMiddleware())
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.RecoveryMiddleware())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"message": "AnyPanel API is running",
		})
	})

	// API v1 路由组
	v1 := r.Group("/api/v1")
	{
		// 认证路由
		SetupAuthRoutes(v1, db, cfg)
		
		// 用户路由
		SetupUserRoutes(v1, db)
		
		// 权限组路由
		SetupPermissionGroupRoutes(v1, db)
		
		// 节点路由
		SetupNodeRoutes(v1, db)
		
		// 商品系统路由
		SetupProductRoutes(v1, db)
	}

	return r
}

// SetupAdminRoutes 设置管理员路由
func SetupAdminRoutes(router *gin.RouterGroup, db *gorm.DB) {
	admin := router.Group("/admin")
	admin.Use(middleware.AuthMiddleware(db))
	admin.Use(middleware.AdminMiddleware())
	{
		// 管理员仪表板
		admin.GET("/dashboard", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "管理员仪表板",
			})
		})
		
		// 系统设置
		admin.GET("/settings", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "系统设置",
			})
		})
		
		// 系统日志
		admin.GET("/logs", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"message": "系统日志",
			})
		})
	}
}