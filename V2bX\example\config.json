{"Log": {"Level": "info", "Output": ""}, "Cores": [{"Type": "sing", "Log": {"Level": "info", "Timestamp": true}, "NTP": {"Enable": false, "Server": "time.apple.com", "ServerPort": 0}, "OriginalPath": "/etc/V2bX/sing_origin.json"}], "Nodes": [{"Core": "sing", "ApiHost": "http://127.0.0.1", "ApiKey": "test", "NodeID": 33, "NodeType": "shadowsocks", "Timeout": 30, "ListenIP": "0.0.0.0", "SendIP": "0.0.0.0", "DeviceOnlineMinTraffic": 200, "TCPFastOpen": false, "SniffEnabled": true, "CertConfig": {"CertMode": "self", "RejectUnknownSni": false, "CertDomain": "example.com", "CertFile": "/etc/V2bX/fullchain.cer", "KeyFile": "/etc/V2bX/cert.key", "Provider": "cloudflare", "DNSEnv": {"EnvName": "env1"}}}]}