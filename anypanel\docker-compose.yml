version: '3.8'

services:
  # 数据库
  mysql:
    image: mysql:8.0
    container_name: anypanel-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: anypanel
      MYSQL_USER: anypanel
      MYSQL_PASSWORD: anypanel123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  # Redis
  redis:
    image: redis:7-alpine
    container_name: anypanel-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: anypanel-backend
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
    volumes:
      - ./backend/config.yaml:/root/config.yaml
      - ./logs:/root/logs
    depends_on:
      - mysql
      - redis
    restart: unless-stopped

  # 前端服务 (开发环境)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: anypanel-frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data: