package api

import (
	"anypanel/internal/service"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// AdminAPI 管理员API控制器
type AdminAPI struct {
	userService            *service.UserService
	nodeService            *service.NodeService
	permissionGroupService *service.PermissionGroupService
	productService         *service.ProductService
	orderService           *service.OrderService
	trafficService         *service.TrafficService
	onlineUserService      *service.OnlineUserService
}

// ========== 用户管理 ==========

// GetUsers 获取用户列表
func (api *AdminAPI) GetUsers(c *gin.Context) {
	var req service.UserQueryRequest
	if err := c.ShouldBind<PERSON>uery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	response, err := api.userService.QueryUsers(&req)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户列表成功",
		"data":    response,
	})
}

// CreateUser 创建用户
func (api *AdminAPI) CreateUser(c *gin.Context) {
	var req service.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	user, err := api.userService.CreateUser(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建用户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "创建用户成功",
		"data":    user,
	})
}

// GetUser 获取用户详情
func (api *AdminAPI) GetUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "用户ID格式错误",
		})
		return
	}

	user, err := api.userService.GetUserByID(uint(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "用户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户详情成功",
		"data":    user,
	})
}

// UpdateUser 更新用户
func (api *AdminAPI) UpdateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "用户ID格式错误",
		})
		return
	}

	var req service.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	user, err := api.userService.UpdateUser(uint(userID), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新用户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新用户成功",
		"data":    user,
	})
}

// DeleteUser 删除用户
func (api *AdminAPI) DeleteUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "用户ID格式错误",
		})
		return
	}

	if err := api.userService.DeleteUser(uint(userID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除用户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除用户成功",
	})
}

// UpdateUserStatus 更新用户状态
func (api *AdminAPI) UpdateUserStatus(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "用户ID格式错误",
		})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required,oneof=active inactive expired"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := api.userService.UpdateUserStatus(uint(userID), req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新用户状态失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新用户状态成功",
	})
}

// ResetUserPassword 重置用户密码
func (api *AdminAPI) ResetUserPassword(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "用户ID格式错误",
		})
		return
	}

	var req struct {
		Password string `json:"password" binding:"required,min=6"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := api.userService.ResetUserPassword(uint(userID), req.Password); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "重置密码失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "重置密码成功",
	})
}

// GetUserTraffic 获取用户流量统计
func (api *AdminAPI) GetUserTraffic(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "用户ID格式错误",
		})
		return
	}

	stats, err := api.userService.GetUserTrafficStats(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取流量统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取流量统计成功",
		"data":    stats,
	})
}

// ========== 节点管理 ==========

// GetNodes 获取节点列表
func (api *AdminAPI) GetNodes(c *gin.Context) {
	var req service.NodeQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	response, err := api.nodeService.QueryNodes(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取节点列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取节点列表成功",
		"data":    response,
	})
}

// CreateNode 创建节点
func (api *AdminAPI) CreateNode(c *gin.Context) {
	var req service.NodeCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	node, err := api.nodeService.CreateNode(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建节点失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "创建节点成功",
		"data":    node,
	})
}

// GetNode 获取节点详情
func (api *AdminAPI) GetNode(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "节点ID格式错误",
		})
		return
	}

	node, err := api.nodeService.GetNodeWithDetails(uint(nodeID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "节点不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取节点详情成功",
		"data":    node,
	})
}

// UpdateNode 更新节点
func (api *AdminAPI) UpdateNode(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "节点ID格式错误",
		})
		return
	}

	var req service.NodeUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	node, err := api.nodeService.UpdateNode(uint(nodeID), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新节点失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新节点成功",
		"data":    node,
	})
}

// DeleteNode 删除节点
func (api *AdminAPI) DeleteNode(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "节点ID格式错误",
		})
		return
	}

	if err := api.nodeService.DeleteNode(uint(nodeID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除节点失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除节点成功",
	})
}

// UpdateNodeStatus 更新节点状态
func (api *AdminAPI) UpdateNodeStatus(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "节点ID格式错误",
		})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required,oneof=online offline maintenance"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := api.nodeService.UpdateNodeStatus(uint(nodeID), req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新节点状态失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新节点状态成功",
	})
}

// GetNodeStats 获取节点统计
func (api *AdminAPI) GetNodeStats(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "节点ID格式错误",
		})
		return
	}

	stats, err := api.nodeService.GetNodeStats(uint(nodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取节点统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取节点统计成功",
		"data":    stats,
	})
}

// HealthCheckNode 节点健康检查
func (api *AdminAPI) HealthCheckNode(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "节点ID格式错误",
		})
		return
	}

	result, err := api.nodeService.HealthCheck(uint(nodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "健康检查失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "健康检查完成",
		"data":    result,
	})
}

// BatchHealthCheck 批量健康检查
func (api *AdminAPI) BatchHealthCheck(c *gin.Context) {
	results, err := api.nodeService.BatchHealthCheck()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "批量健康检查失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量健康检查完成",
		"data":    results,
	})
}

// ========== 权限组管理 ==========

// GetPermissionGroups 获取权限组列表
func (api *AdminAPI) GetPermissionGroups(c *gin.Context) {
	var req service.PermissionGroupQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	response, err := api.permissionGroupService.QueryPermissionGroups(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取权限组列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取权限组列表成功",
		"data":    response,
	})
}

// CreatePermissionGroup 创建权限组
func (api *AdminAPI) CreatePermissionGroup(c *gin.Context) {
	var req service.PermissionGroupCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	group, err := api.permissionGroupService.CreatePermissionGroup(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建权限组失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "创建权限组成功",
		"data":    group,
	})
}

// GetPermissionGroup 获取权限组详情
func (api *AdminAPI) GetPermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "权限组ID格式错误",
		})
		return
	}

	group, err := api.permissionGroupService.GetPermissionGroupWithDetails(uint(groupID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "权限组不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取权限组详情成功",
		"data":    group,
	})
}

// UpdatePermissionGroup 更新权限组
func (api *AdminAPI) UpdatePermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "权限组ID格式错误",
		})
		return
	}

	var req service.PermissionGroupUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	group, err := api.permissionGroupService.UpdatePermissionGroup(uint(groupID), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新权限组失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新权限组成功",
		"data":    group,
	})
}

// DeletePermissionGroup 删除权限组
func (api *AdminAPI) DeletePermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "权限组ID格式错误",
		})
		return
	}

	if err := api.permissionGroupService.DeletePermissionGroup(uint(groupID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除权限组失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除权限组成功",
	})
}

// GetPermissionGroupNodes 获取权限组节点
func (api *AdminAPI) GetPermissionGroupNodes(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "权限组ID格式错误",
		})
		return
	}

	nodes, err := api.permissionGroupService.GetPermissionGroupNodes(uint(groupID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取权限组节点失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取权限组节点成功",
		"data":    nodes,
	})
}

// AddNodeToPermissionGroup 向权限组添加节点
func (api *AdminAPI) AddNodeToPermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "权限组ID格式错误",
		})
		return
	}

	var req struct {
		NodeID uint `json:"node_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := api.permissionGroupService.AddNodeToPermissionGroup(uint(groupID), req.NodeID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "添加节点到权限组失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "添加节点到权限组成功",
	})
}

// RemoveNodeFromPermissionGroup 从权限组移除节点
func (api *AdminAPI) RemoveNodeFromPermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "权限组ID格式错误",
		})
		return
	}

	nodeIDStr := c.Param("nodeId")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "节点ID格式错误",
		})
		return
	}

	if err := api.permissionGroupService.RemoveNodeFromPermissionGroup(uint(groupID), uint(nodeID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "从权限组移除节点失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "从权限组移除节点成功",
	})
}

// SetPermissionGroupNodes 设置权限组节点
func (api *AdminAPI) SetPermissionGroupNodes(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "权限组ID格式错误",
		})
		return
	}

	var req struct {
		NodeIDs []uint `json:"node_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := api.permissionGroupService.SetPermissionGroupNodes(uint(groupID), req.NodeIDs); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "设置权限组节点失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "设置权限组节点成功",
	})
}

// ========== 商品管理 ==========

// GetProducts 获取商品列表
func (api *AdminAPI) GetProducts(c *gin.Context) {
	// 简化实现，实际应该支持分页和搜索
	products, err := api.productService.GetAllProducts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取商品列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取商品列表成功",
		"data":    products,
	})
}

// CreateProduct 创建商品
func (api *AdminAPI) CreateProduct(c *gin.Context) {
	var req service.ProductCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	product, err := api.productService.CreateProduct(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建商品失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "创建商品成功",
		"data":    product,
	})
}

// GetProduct 获取商品详情
func (api *AdminAPI) GetProduct(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "商品ID格式错误",
		})
		return
	}

	product, err := api.productService.GetProductByID(uint(productID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "商品不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取商品详情成功",
		"data":    product,
	})
}

// UpdateProduct 更新商品
func (api *AdminAPI) UpdateProduct(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "商品ID格式错误",
		})
		return
	}

	var req service.ProductUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	product, err := api.productService.UpdateProduct(uint(productID), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新商品失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新商品成功",
		"data":    product,
	})
}

// DeleteProduct 删除商品
func (api *AdminAPI) DeleteProduct(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := strconv.ParseUint(productIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "商品ID格式错误",
		})
		return
	}

	if err := api.productService.DeleteProduct(uint(productID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除商品失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除商品成功",
	})
}

// ========== 订单管理 ==========

// GetOrders 获取订单列表
func (api *AdminAPI) GetOrders(c *gin.Context) {
	// 简化实现，实际应该支持分页和搜索
	orders, err := api.orderService.GetAllOrders()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取订单列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取订单列表成功",
		"data":    orders,
	})
}

// GetOrder 获取订单详情
func (api *AdminAPI) GetOrder(c *gin.Context) {
	orderIDStr := c.Param("id")
	order, err := api.orderService.GetOrderByID(orderIDStr)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "订单不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取订单详情成功",
		"data":    order,
	})
}

// UpdateOrderStatus 更新订单状态
func (api *AdminAPI) UpdateOrderStatus(c *gin.Context) {
	orderIDStr := c.Param("id")
	var req struct {
		Status string `json:"status" binding:"required,oneof=pending paid cancelled refunded"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := api.orderService.UpdateOrderStatus(orderIDStr, req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新订单状态失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新订单状态成功",
	})
}

// ========== 统计分析 ==========

// GetDashboardStats 获取仪表板统计
func (api *AdminAPI) GetDashboardStats(c *gin.Context) {
	// 获取用户统计
	totalUsers, err := api.userService.GetTotalUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户统计失败",
			"error":   err.Error(),
		})
		return
	}

	activeUsers, err := api.userService.GetActiveUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取活跃用户统计失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取节点统计
	totalNodes, err := api.nodeService.GetTotalNodes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取节点统计失败",
			"error":   err.Error(),
		})
		return
	}

	onlineNodes, err := api.nodeService.GetOnlineNodes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取在线节点统计失败",
			"error":   err.Error(),
		})
		return
	}

	// 获取在线用户统计
	onlineUserCount, err := api.onlineUserService.GetTotalOnlineUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取在线用户统计失败",
			"error":   err.Error(),
		})
		return
	}

	stats := gin.H{
		"total_users":    totalUsers,
		"active_users":   len(activeUsers),
		"total_nodes":    totalNodes,
		"online_nodes":   len(onlineNodes),
		"online_users":   onlineUserCount,
		"generated_at":    time.Now().Format("2006-01-02 15:04:05"),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取仪表板统计成功",
		"data":    stats,
	})
}

// GetTrafficStats 获取流量统计
func (api *AdminAPI) GetTrafficStats(c *gin.Context) {
	// 简化实现，实际应该支持时间范围筛选
	stats, err := api.trafficService.GetTrafficStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取流量统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取流量统计成功",
		"data":    stats,
	})
}

// GetOnlineUserStats 获取在线用户统计
func (api *AdminAPI) GetOnlineUserStats(c *gin.Context) {
	stats, err := api.onlineUserService.GetOnlineUserStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取在线用户统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取在线用户统计成功",
		"data":    stats,
	})
}

// GetRevenueStats 获取收入统计
func (api *AdminAPI) GetRevenueStats(c *gin.Context) {
	// 简化实现，实际应该支持时间范围筛选
	stats, err := api.orderService.GetRevenueStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取收入统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取收入统计成功",
		"data":    stats,
	})
}

// ========== 系统管理和维护 ==========

// GetSystemStats 获取系统统计信息
func (api *AdminAPI) GetSystemStats(c *gin.Context) {
	// 创建调度器实例来获取系统统计
	scheduler := NewTaskScheduler(api.db)
	
	stats, err := scheduler.GetSystemCleanupStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取系统统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取系统统计成功",
		"data":    stats,
	})
}

// RunCleanupTask 运行清理任务
func (api *AdminAPI) RunCleanupTask(c *gin.Context) {
	var req struct {
		TaskName string `json:"task_name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 创建调度器实例
	scheduler := NewTaskScheduler(api.db)

	// 立即运行指定任务
	if err := scheduler.RunTaskNow(req.TaskName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "运行任务失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("任务 %s 执行成功", req.TaskName),
	})
}

// GetTrafficSystemStats 获取流量系统统计
func (api *AdminAPI) GetTrafficSystemStats(c *gin.Context) {
	stats, err := api.trafficService.GetTrafficSystemStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取流量系统统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取流量系统统计成功",
		"data":    stats,
	})
}

// GetOnlineUserStats 获取在线用户统计
func (api *AdminAPI) GetOnlineUserStats(c *gin.Context) {
	stats, err := api.onlineUserService.GetOnlineUserCleanupStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取在线用户统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取在线用户统计成功",
		"data":    stats,
	})
}

// OptimizeSystem 优化系统性能
func (api *AdminAPI) OptimizeSystem(c *gin.Context) {
	var req struct {
		OptimizeTrafficTable bool `json:"optimize_traffic_table"`
		CleanupTrafficLogs    bool `json:"cleanup_traffic_logs"`
		CleanupOnlineUsers    bool `json:"cleanup_online_users"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	results := make(map[string]interface{})

	// 优化流量表
	if req.OptimizeTrafficTable {
		if err := api.trafficService.OptimizeTrafficTable(); err != nil {
			results["optimize_traffic_table"] = gin.H{"success": false, "error": err.Error()}
		} else {
			results["optimize_traffic_table"] = gin.H{"success": true}
		}
	}

	// 清理流量日志
	if req.CleanupTrafficLogs {
		if err := api.trafficService.CleanupExpiredTrafficLogs(90); err != nil {
			results["cleanup_traffic_logs"] = gin.H{"success": false, "error": err.Error()}
		} else {
			results["cleanup_traffic_logs"] = gin.H{"success": true}
		}
	}

	// 清理在线用户
	if req.CleanupOnlineUsers {
		if err := api.onlineUserService.AutoCleanupExpiredOnlineUsers(); err != nil {
			results["cleanup_online_users"] = gin.H{"success": false, "error": err.Error()}
		} else {
			results["cleanup_online_users"] = gin.H{"success": true}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "系统优化完成",
		"data":    results,
	})
}