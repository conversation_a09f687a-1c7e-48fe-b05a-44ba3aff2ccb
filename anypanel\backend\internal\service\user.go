package service

import (
	"anypanel/internal/model"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type UserService struct {
	db *gorm.DB
}

func NewUserService(db *gorm.DB) *UserService {
	return &UserService{
		db: db,
	}
}

// UserQueryRequest 用户查询请求
type UserQueryRequest struct {
	Page       int    `form:"page" binding:"min=1"`
	PageSize   int    `form:"page_size" binding:"min=1,max=100"`
	Search     string `form:"search"`
	Role       string `form:"role"`
	Status     string `form:"status"`
	SortBy     string `form:"sort_by"`
	SortOrder  string `form:"sort_order"`
}

// UserQueryResponse 用户查询响应
type UserQueryResponse struct {
	Total int64      `json:"total"`
	Users []*UserDTO `json:"users"`
}

// UserCreateRequest 用户创建请求
type UserCreateRequest struct {
	Username     string `json:"username" binding:"required,min=3,max=50"`
	Email        string `json:"email" binding:"required,email"`
	Password     string `json:"password" binding:"required,min=6"`
	Role         string `json:"role" binding:"oneof=admin user"`
	Status       string `json:"status" binding:"oneof=active inactive expired"`
	TrafficLimit int64  `json:"traffic_limit"`
	DeviceLimit  int    `json:"device_limit" binding:"min=1"`
	SpeedLimit   int    `json:"speed_limit" binding:"min=0"`
	ExpiredAt    *string `json:"expired_at"`
}

// UserUpdateRequest 用户更新请求
type UserUpdateRequest struct {
	Username     *string `json:"username"`
	Email        *string `json:"email"`
	Role         *string `json:"role" binding:"omitempty,oneof=admin user"`
	Status       *string `json:"status" binding:"omitempty,oneof=active inactive expired"`
	TrafficLimit *int64  `json:"traffic_limit"`
	DeviceLimit  *int    `json:"device_limit" binding:"omitempty,min=1"`
	SpeedLimit   *int    `json:"speed_limit" binding:"omitempty,min=0"`
	ExpiredAt    *string `json:"expired_at"`
}

// UserDTO 用户数据传输对象
type UserDTO struct {
	ID           uint       `json:"id"`
	Username     string     `json:"username"`
	Email        string     `json:"email"`
	Role         string     `json:"role"`
	Status       string     `json:"status"`
	TrafficLimit int64      `json:"traffic_limit"`
	TrafficUsed  int64      `json:"traffic_used"`
	DeviceLimit  int        `json:"device_limit"`
	SpeedLimit   int        `json:"speed_limit"`
	ExpiredAt    *time.Time `json:"expired_at"`
	CreatedAt    time.Time  `json:"created_at"`
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(userID uint) (*model.User, error) {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	return &user, nil
}

// GetUserByUUID 根据UUID获取用户
func (s *UserService) GetUserByUUID(uuid string) (*model.User, error) {
	var user model.User
	if err := s.db.Where("uuid = ?", uuid).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	return &user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *UserService) GetUserByUsername(username string) (*model.User, error) {
	var user model.User
	if err := s.db.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	return &user, nil
}

// GetUserByEmail 根据邮箱获取用户
func (s *UserService) GetUserByEmail(email string) (*model.User, error) {
	var user model.User
	if err := s.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	return &user, nil
}

// QueryUsers 查询用户列表
func (s *UserService) QueryUsers(req *UserQueryRequest) (*UserQueryResponse, error) {
	query := s.db.Model(&model.User{})

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("username LIKE ? OR email LIKE ? OR uuid LIKE ?", searchPattern, searchPattern, searchPattern)
	}

	// 角色筛选
	if req.Role != "" {
		query = query.Where("role = ?", req.Role)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询用户
	var users []model.User
	if err := query.Find(&users).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	userDTOs := make([]*UserDTO, len(users))
	for i, user := range users {
		userDTOs[i] = s.toUserDTO(&user)
	}

	return &UserQueryResponse{
		Total: total,
		Users: userDTOs,
	}, nil
}

// CreateUser 创建用户
func (s *UserService) CreateUser(req *UserCreateRequest) (*UserDTO, error) {
	// 检查用户名是否已存在
	if _, err := s.GetUserByUsername(req.Username); err == nil {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if _, err := s.GetUserByEmail(req.Email); err == nil {
		return nil, errors.New("邮箱已存在")
	}

	// 加密密码
	hashedPassword, err := hashPassword(req.Password)
	if err != nil {
		return nil, err
	}

	// 生成UUID
	userUUID := uuid.New().String()

	// 创建用户
	user := &model.User{
		UUID:         userUUID,
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: string(hashedPassword),
		Role:         req.Role,
		Status:       req.Status,
		TrafficLimit: req.TrafficLimit,
		TrafficUsed:  0,
		DeviceLimit:  req.DeviceLimit,
		SpeedLimit:   req.SpeedLimit,
	}

	// 处理过期时间
	if req.ExpiredAt != nil && *req.ExpiredAt != "" {
		expiredAt, err := time.Parse("2006-01-02", *req.ExpiredAt)
		if err != nil {
			return nil, errors.New("过期时间格式错误，请使用 YYYY-MM-DD 格式")
		}
		user.ExpiredAt = &expiredAt
	}

	if err := user.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, err
	}

	return s.toUserDTO(user), nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(userID uint, req *UserUpdateRequest) (*UserDTO, error) {
	user, err := s.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Username != nil {
		// 检查用户名是否已被其他用户使用
		var existingUser model.User
		if err := s.db.Where("username = ? AND id != ?", *req.Username, userID).First(&existingUser).Error; err == nil {
			return nil, errors.New("用户名已存在")
		}
		user.Username = *req.Username
	}

	if req.Email != nil {
		// 检查邮箱是否已被其他用户使用
		var existingUser model.User
		if err := s.db.Where("email = ? AND id != ?", *req.Email, userID).First(&existingUser).Error; err == nil {
			return nil, errors.New("邮箱已存在")
		}
		user.Email = *req.Email
	}

	if req.Role != nil {
		user.Role = *req.Role
	}

	if req.Status != nil {
		user.Status = *req.Status
	}

	if req.TrafficLimit != nil {
		user.TrafficLimit = *req.TrafficLimit
	}

	if req.DeviceLimit != nil {
		user.DeviceLimit = *req.DeviceLimit
	}

	if req.SpeedLimit != nil {
		user.SpeedLimit = *req.SpeedLimit
	}

	if req.ExpiredAt != nil {
		if *req.ExpiredAt != "" {
			expiredAt, err := time.Parse("2006-01-02", *req.ExpiredAt)
			if err != nil {
				return nil, errors.New("过期时间格式错误，请使用 YYYY-MM-DD 格式")
			}
			user.ExpiredAt = &expiredAt
		} else {
			user.ExpiredAt = nil
		}
	}

	if err := user.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(user).Error; err != nil {
		return nil, err
	}

	return s.toUserDTO(user), nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(userID uint) error {
	user, err := s.GetUserByID(userID)
	if err != nil {
		return err
	}

	if err := s.db.Delete(user).Error; err != nil {
		return err
	}

	return nil
}

// UpdateUserStatus 更新用户状态
func (s *UserService) UpdateUserStatus(userID uint, status string) error {
	user, err := s.GetUserByID(userID)
	if err != nil {
		return err
	}

	user.Status = status
	if err := user.Validate(); err != nil {
		return err
	}

	return s.db.Save(user).Error
}

// UpdateUserTraffic 更新用户流量
func (s *UserService) UpdateUserTraffic(userID uint, trafficUsed int64) error {
	user, err := s.GetUserByID(userID)
	if err != nil {
		return err
	}

	user.TrafficUsed = trafficUsed
	return s.db.Save(user).Error
}

// GetUserTrafficStats 获取用户流量统计
func (s *UserService) GetUserTrafficStats(userID uint) (map[string]interface{}, error) {
	user, err := s.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	availableTraffic := user.GetAvailableTraffic()
	usagePercentage := float64(0)
	if user.TrafficLimit > 0 {
		usagePercentage = float64(user.TrafficUsed) / float64(user.TrafficLimit) * 100
	}

	stats := map[string]interface{}{
		"traffic_limit":     user.TrafficLimit,
		"traffic_used":      user.TrafficUsed,
		"available_traffic": availableTraffic,
		"usage_percentage":  usagePercentage,
		"is_unlimited":      user.TrafficLimit == 0,
	}

	return stats, nil
}

// CheckUserCanConnect 检查用户是否可以连接
func (s *UserService) CheckUserCanConnect(userID uint) (bool, error) {
	user, err := s.GetUserByID(userID)
	if err != nil {
		return false, err
	}

	return user.CanConnect(), nil
}

// GetActiveUsers 获取活跃用户列表
func (s *UserService) GetActiveUsers() ([]*model.User, error) {
	var users []model.User
	if err := s.db.Where("status = ?", "active").Find(&users).Error; err != nil {
		return nil, err
	}

	activeUsers := make([]*model.User, len(users))
	for i := range users {
		activeUsers[i] = &users[i]
	}

	return activeUsers, nil
}

// GetExpiredUsers 获取过期用户列表
func (s *UserService) GetExpiredUsers() ([]*model.User, error) {
	var users []model.User
	if err := s.db.Where("status = ? OR (expired_at IS NOT NULL AND expired_at < NOW())", "expired").Find(&users).Error; err != nil {
		return nil, err
	}

	expiredUsers := make([]*model.User, len(users))
	for i := range users {
		expiredUsers[i] = &users[i]
	}

	return expiredUsers, nil
}

// ResetUserPassword 重置用户密码
func (s *UserService) ResetUserPassword(userID uint, newPassword string) error {
	user, err := s.GetUserByID(userID)
	if err != nil {
		return err
	}

	hashedPassword, err := hashPassword(newPassword)
	if err != nil {
		return err
	}

	user.PasswordHash = string(hashedPassword)
	return s.db.Save(user).Error
}

// 辅助函数
func (s *UserService) toUserDTO(user *model.User) *UserDTO {
	return &UserDTO{
		ID:           user.ID,
		Username:     user.Username,
		Email:        user.Email,
		Role:         user.Role,
		Status:       user.Status,
		TrafficLimit: user.TrafficLimit,
		TrafficUsed:  user.TrafficUsed,
		DeviceLimit:  user.DeviceLimit,
		SpeedLimit:   user.SpeedLimit,
		ExpiredAt:    user.ExpiredAt,
		CreatedAt:    user.CreatedAt,
	}
}

// hashPassword 密码加密
func hashPassword(password string) ([]byte, error) {
	return bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
}