@echo off
echo 🗄️ 初始化本地数据库...

:: 数据库配置
set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_NAME=anypanel

:: 获取数据库密码
set /p DB_PASSWORD=请输入MySQL root密码: 

:: 检查MySQL连接
echo 🔍 检查MySQL连接...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL连接失败，请检查配置
    pause
    exit /b 1
)

echo ✅ MySQL连接成功

:: 创建数据库
echo 📊 创建数据库...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if errorlevel 1 (
    echo ❌ 数据库创建失败
    pause
    exit /b 1
)

echo ✅ 数据库创建成功

:: 导入初始数据
echo 📥 导入初始数据...
if exist "scripts\init.sql" (
    mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASSWORD% %DB_NAME% < scripts\init.sql
    
    if errorlevel 1 (
        echo ❌ 初始数据导入失败
        pause
        exit /b 1
    )
) else (
    echo ❌ 初始化SQL文件不存在: scripts\init.sql
    pause
    exit /b 1
)

echo ✅ 初始数据导入成功

echo 🎉 数据库初始化完成！
echo.
echo 💡 现在可以启动开发环境了：
echo    Windows: .\scripts\dev-local.bat
echo    Linux/Mac: ./scripts/dev-local.sh
echo.
pause