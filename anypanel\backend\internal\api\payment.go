package api

import (
	"anypanel/internal/model"
	"anypanel/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PaymentAPI 支付API
type PaymentAPI struct {
	paymentService *service.PaymentService
	orderService   *service.OrderService
}

// NewPaymentAPI 创建支付API
func NewPaymentAPI(paymentService *service.PaymentService, orderService *service.OrderService) *PaymentAPI {
	return &PaymentAPI{
		paymentService: paymentService,
		orderService:   orderService,
	}
}

// GetPaymentMethods 获取支付方式列表
// @Summary 获取支付方式列表
// @Description 获取可用的支付方式列表
// @Tags 支付
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param enable query bool false "启用状态"
// @Param method query string false "支付方式"
// @Success 200 {object} Response{data=service.PaymentQueryResponse}
// @Router /payment/methods [get]
func (api *PaymentAPI) GetPaymentMethods(c *gin.Context) {
	var req service.PaymentQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		Error(c, 400, err.Error())
		return
	}

	result, err := api.paymentService.QueryPaymentMethods(&req)
	if err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, result)
}

// GetPaymentMethod 获取支付方式详情
// @Summary 获取支付方式详情
// @Description 获取指定支付方式的详细信息
// @Tags 支付
// @Accept json
// @Produce json
// @Param method path string true "支付方式"
// @Success 200 {object} Response{data=service.PaymentMethodConfig}
// @Router /payment/methods/{method} [get]
func (api *PaymentAPI) GetPaymentMethod(c *gin.Context) {
	method := c.Param("method")
	if method == "" {
		Error(c, 400, "支付方式不能为空")
		return
	}

	payment, err := api.paymentService.GetPaymentMethod(method)
	if err != nil {
		Error(c, 404, err.Error())
		return
	}

	result := api.paymentService.ToPaymentMethodConfig(payment)
	Success(c, result)
}

// GetPaymentForm 获取支付方式配置表单
// @Summary 获取支付方式配置表单
// @Description 获取指定支付方式的配置表单
// @Tags 支付
// @Accept json
// @Produce json
// @Param method path string true "支付方式"
// @Success 200 {object} Response{data=map[string]service.FormField}
// @Router /payment/methods/{method}/form [get]
func (api *PaymentAPI) GetPaymentForm(c *gin.Context) {
	method := c.Param("method")
	if method == "" {
		Error(c, 400, "支付方式不能为空")
		return
	}

	form, err := api.paymentService.GetPaymentForm(method)
	if err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, form)
}

// CreatePaymentMethod 创建支付方式
// @Summary 创建支付方式
// @Description 创建新的支付方式
// @Tags 支付
// @Accept json
// @Produce json
// @Param request body service.PaymentConfigRequest true "支付方式配置"
// @Success 200 {object} Response{data=service.PaymentMethodConfig}
// @Router /payment/methods [post]
func (api *PaymentAPI) CreatePaymentMethod(c *gin.Context) {
	var req service.PaymentConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		Error(c, 400, err.Error())
		return
	}

	result, err := api.paymentService.CreatePaymentMethod(&req)
	if err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, result)
}

// UpdatePaymentMethod 更新支付方式
// @Summary 更新支付方式
// @Description 更新指定的支付方式
// @Tags 支付
// @Accept json
// @Produce json
// @Param id path int true "支付方式ID"
// @Param request body service.PaymentConfigRequest true "支付方式配置"
// @Success 200 {object} Response{data=service.PaymentMethodConfig}
// @Router /payment/methods/{id} [put]
func (api *PaymentAPI) UpdatePaymentMethod(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		Error(c, 400, "无效的支付方式ID")
		return
	}

	var req service.PaymentConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		Error(c, 400, err.Error())
		return
	}

	result, err := api.paymentService.UpdatePaymentMethod(uint(id), &req)
	if err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, result)
}

// DeletePaymentMethod 删除支付方式
// @Summary 删除支付方式
// @Description 删除指定的支付方式
// @Tags 支付
// @Accept json
// @Produce json
// @Param id path int true "支付方式ID"
// @Success 200 {object} Response
// @Router /payment/methods/{id} [delete]
func (api *PaymentAPI) DeletePaymentMethod(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		Error(c, 400, "无效的支付方式ID")
		return
	}

	if err := api.paymentService.DeletePaymentMethod(uint(id)); err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, nil)
}

// CreatePayment 创建支付订单
// @Summary 创建支付订单
// @Description 创建支付订单并返回支付信息
// @Tags 支付
// @Accept json
// @Produce json
// @Param request body CreatePaymentRequest true "创建支付请求"
// @Success 200 {object} Response{data=service.PaymentResult}
// @Router /payment/create [post]
func (api *PaymentAPI) CreatePayment(c *gin.Context) {
	var req CreatePaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		Error(c, 400, err.Error())
		return
	}

	// 获取订单信息
	order, err := api.orderService.GetOrderByOrderID(req.OrderID)
	if err != nil {
		Error(c, 404, "订单不存在")
		return
	}

	// 检查订单状态
	if order.Status != "pending" {
		Error(c, 400, "订单状态不正确")
		return
	}

	// 创建支付
	result, err := api.paymentService.CreatePayment(req.Method, order)
	if err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, result)
}

// PaymentNotify 支付回调
// @Summary 支付回调
// @Description 处理支付平台的回调通知
// @Tags 支付
// @Accept json
// @Produce json
// @Param method path string true "支付方式"
// @Param uuid path string true "支付方式UUID"
// @Success 200 {string} string "success"
// @Router /payment/notify/{method}/{uuid} [post]
func (api *PaymentAPI) PaymentNotify(c *gin.Context) {
	method := c.Param("method")
	uuid := c.Param("uuid")
	
	if method == "" || uuid == "" {
		c.String(400, "参数错误")
		return
	}

	// 解析回调参数
	params := make(map[string]interface{})
	
	// 处理JSON格式回调
	if c.ContentType() == "application/json" {
		var jsonParams map[string]interface{}
		if err := c.ShouldBindJSON(&jsonParams); err == nil {
			params = jsonParams
		}
	}
	
	// 处理表单格式回调
	if len(params) == 0 {
		for k, v := range c.Request.Form {
			if len(v) > 0 {
				params[k] = v[0]
			}
		}
	}
	
	// 处理查询参数
	if len(params) == 0 {
		for k, v := range c.Request.URL.Query() {
			if len(v) > 0 {
				params[k] = v[0]
			}
		}
	}

	// 处理支付回调
	result, err := api.paymentService.ProcessPaymentNotify(method, uuid, params)
	if err != nil {
		c.String(500, "处理失败")
		return
	}

	// 更新订单状态
	if err := api.handlePaymentSuccess(result.TradeNo, result.CallbackNo); err != nil {
		c.String(500, "订单更新失败")
		return
	}

	// 返回成功响应
	if result.CustomData != "" {
		c.String(200, result.CustomData)
	} else {
		c.String(200, "success")
	}
}

// GetPaymentLogs 获取支付日志
// @Summary 获取支付日志
// @Description 获取支付日志列表
// @Tags 支付
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param order_id query string false "订单ID"
// @Param method query string false "支付方式"
// @Param status query string false "支付状态"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} Response{data=service.PaymentLogQueryResponse}
// @Router /payment/logs [get]
func (api *PaymentAPI) GetPaymentLogs(c *gin.Context) {
	var req service.PaymentLogQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		Error(c, 400, err.Error())
		return
	}

	result, err := api.paymentService.QueryPaymentLogs(&req)
	if err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, result)
}

// GetPaymentStats 获取支付统计
// @Summary 获取支付统计
// @Description 获取支付相关的统计信息
// @Tags 支付
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=map[string]interface{}}
// @Router /payment/stats [get]
func (api *PaymentAPI) GetPaymentStats(c *gin.Context) {
	result, err := api.paymentService.GetPaymentStats()
	if err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, result)
}

// ProcessRefund 处理退款
// @Summary 处理退款
// @Description 处理退款请求
// @Tags 支付
// @Accept json
// @Produce json
// @Param request body service.PaymentRefundOrder true "退款请求"
// @Success 200 {object} Response{data=service.PaymentRefundResult}
// @Router /payment/refund [post]
func (api *PaymentAPI) ProcessRefund(c *gin.Context) {
	var req service.PaymentRefundOrder
	if err := c.ShouldBindJSON(&req); err != nil {
		Error(c, 400, err.Error())
		return
	}

	result, err := api.paymentService.ProcessRefund(req.Method, req)
	if err != nil {
		Error(c, 500, err.Error())
		return
	}

	Success(c, result)
}

// 内部方法

// handlePaymentSuccess 处理支付成功
func (api *PaymentAPI) handlePaymentSuccess(orderID, paymentID string) error {
	// 获取订单信息
	order, err := api.orderService.GetOrderByOrderID(orderID)
	if err != nil {
		return err
	}

	// 检查订单状态
	if order.Status == "paid" {
		return nil // 已经支付过
	}

	// 更新订单状态
	if err := api.orderService.UpdateOrderStatus(orderID, "paid", paymentID); err != nil {
		return err
	}

	// 创建订阅
	if _, err := api.orderService.CreateSubscriptionFromOrder(orderID); err != nil {
		// 订阅创建失败，但订单已支付，记录日志
		// 这里可以添加日志记录
	}

	return nil
}

// 请求结构体

// CreatePaymentRequest 创建支付请求
type CreatePaymentRequest struct {
	OrderID string `json:"order_id" binding:"required"`
	Method  string `json:"method" binding:"required"`
}