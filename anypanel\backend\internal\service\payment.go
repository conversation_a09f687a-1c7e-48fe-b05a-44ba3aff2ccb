package service

import (
	"anypanel/internal/model"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PaymentService 支付服务
type PaymentService struct {
	db *gorm.DB
}

// NewPaymentService 创建支付服务
func NewPaymentService(db *gorm.DB) *PaymentService {
	return &PaymentService{db: db}
}

// PaymentGateway 支付网关接口
type PaymentGateway interface {
	// Form 获取支付配置表单
	Form() map[string]FormField
	
	// Pay 发起支付
	Pay(order PaymentOrder) (*PaymentResult, error)
	
	// Notify 处理支付回调
	Notify(params map[string]interface{}) (*PaymentNotifyResult, error)
	
	// Refund 处理退款
	Refund(refundOrder PaymentRefundOrder) (*PaymentRefundResult, error)
}

// FormField 表单字段
type FormField struct {
	Label       string      `json:"label"`
	Description string      `json:"description"`
	Type        string      `json:"type"`      // input, select, textarea, etc.
	Required    bool        `json:"required"`
	Options     []string    `json:"options"`   // for select
	Value       interface{} `json:"value"`     // default value
}

// PaymentOrder 支付订单信息
type PaymentOrder struct {
	OrderID     string  `json:"order_id"`
	TotalAmount float64 `json:"total_amount"`
	UserID      uint    `json:"user_id"`
	NotifyURL   string  `json:"notify_url"`
	ReturnURL   string  `json:"return_url"`
	Subject     string  `json:"subject"`
	Body        string  `json:"body"`
}

// PaymentResult 支付结果
type PaymentResult struct {
	Type          int         `json:"type"`           // 0: qrcode, 1: url, 2: form
	Data          string      `json:"data"`          // qrcode content, url, or form html
	CustomResult  string      `json:"custom_result"`  // custom response content
	TransactionID string      `json:"transaction_id"` // payment platform transaction id
}

// PaymentNotifyResult 支付回调结果
type PaymentNotifyResult struct {
	TradeNo     string `json:"trade_no"`     // our order id
	CallbackNo  string `json:"callback_no"`  // payment platform transaction id
	Amount      string `json:"amount"`       // payment amount
	Status      string `json:"status"`       // payment status
	CustomData  string `json:"custom_data"`  // custom response data
}

// PaymentRefundOrder 退款订单
type PaymentRefundOrder struct {
	OrderID        string  `json:"order_id"`
	RefundID       string  `json:"refund_id"`
	RefundAmount   float64 `json:"refund_amount"`
	RefundReason   string  `json:"refund_reason"`
	NotifyURL      string  `json:"notify_url"`
	OriginalAmount float64 `json:"original_amount"`
}

// PaymentRefundResult 退款结果
type PaymentRefundResult struct {
	Success       bool   `json:"success"`
	RefundID      string `json:"refund_id"`
	TransactionID string `json:"transaction_id"`
	Message       string `json:"message"`
}

// PaymentMethodConfig 支付方式配置
type PaymentMethodConfig struct {
	ID           uint                   `json:"id"`
	Name         string                 `json:"name"`
	Method       string                 `json:"method"`
	Config       map[string]interface{} `json:"config"`
	Enable       bool                   `json:"enable"`
	SortOrder    int                    `json:"sort_order"`
	NotifyDomain string                 `json:"notify_domain"`
	UUID         string                 `json:"uuid"`
}

// PaymentConfigRequest 支付配置请求
type PaymentConfigRequest struct {
	Name         string                 `json:"name" binding:"required"`
	Method       string                 `json:"method" binding:"required"`
	Config       map[string]interface{} `json:"config"`
	Enable       bool                   `json:"enable"`
	SortOrder    int                    `json:"sort_order"`
	NotifyDomain string                 `json:"notify_domain"`
}

// PaymentQueryRequest 支付方式查询请求
type PaymentQueryRequest struct {
	Page     int    `form:"page" binding:"min=1"`
	PageSize int    `form:"page_size" binding:"min=1,max=100"`
	Enable   *bool  `form:"enable"`
	Method   string `form:"method"`
}

// PaymentQueryResponse 支付方式查询响应
type PaymentQueryResponse struct {
	Total     int64                 `json:"total"`
	Payments []*PaymentMethodConfig `json:"payments"`
}

// PaymentLogQueryRequest 支付日志查询请求
type PaymentLogQueryRequest struct {
	Page       int    `form:"page" binding:"min=1"`
	PageSize   int    `form:"page_size" binding:"min=1,max=100"`
	OrderID    string `form:"order_id"`
	Method     string `form:"method"`
	Status     string `form:"status"`
	StartDate  string `form:"start_date"`
	EndDate    string `form:"end_date"`
}

// PaymentLogQueryResponse 支付日志查询响应
type PaymentLogQueryResponse struct {
	Total       int64          `json:"total"`
	PaymentLogs []*PaymentLogDTO `json:"payment_logs"`
}

// PaymentLogDTO 支付日志数据传输对象
type PaymentLogDTO struct {
	ID           uint                   `json:"id"`
	OrderID      string                 `json:"order_id"`
	PaymentID    string                 `json:"payment_id"`
	Method       string                 `json:"method"`
	Amount       float64                `json:"amount"`
	Status       string                 `json:"status"`
	CallbackData map[string]interface{} `json:"callback_data"`
	ErrorMessage string                 `json:"error_message"`
	CreatedAt    string                 `json:"created_at"`
}

// GetPaymentMethod 获取支付方式
func (s *PaymentService) GetPaymentMethod(method string) (*model.Payment, error) {
	var payment model.Payment
	if err := s.db.Where("method = ? AND enable = ?", method, true).First(&payment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("支付方式不存在或已禁用")
		}
		return nil, err
	}
	return &payment, nil
}

// GetPaymentMethodByUUID 根据UUID获取支付方式
func (s *PaymentService) GetPaymentMethodByUUID(uuid string) (*model.Payment, error) {
	var payment model.Payment
	if err := s.db.Where("uuid = ? AND enable = ?", uuid, true).First(&payment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("支付方式不存在或已禁用")
		}
		return nil, err
	}
	return &payment, nil
}

// QueryPaymentMethods 查询支付方式列表
func (s *PaymentService) QueryPaymentMethods(req *PaymentQueryRequest) (*PaymentQueryResponse, error) {
	query := s.db.Model(&model.Payment{})

	// 启用状态筛选
	if req.Enable != nil {
		query = query.Where("enable = ?", *req.Enable)
	}

	// 支付方式筛选
	if req.Method != "" {
		query = query.Where("method = ?", req.Method)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	query = query.Order("sort_order ASC, created_at DESC")

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询支付方式
	var payments []model.Payment
	if err := query.Find(&payments).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	paymentDTOs := make([]*PaymentMethodConfig, len(payments))
	for i, payment := range payments {
		paymentDTOs[i] = s.toPaymentMethodConfig(&payment)
	}

	return &PaymentQueryResponse{
		Total:     total,
		Payments: paymentDTOs,
	}, nil
}

// CreatePaymentMethod 创建支付方式
func (s *PaymentService) CreatePaymentMethod(req *PaymentConfigRequest) (*PaymentMethodConfig, error) {
	// 检查支付方式是否已存在
	var existingPayment model.Payment
	if err := s.db.Where("method = ?", req.Method).First(&existingPayment).Error; err == nil {
		return nil, errors.New("支付方式已存在")
	}

	// 创建支付方式
	payment := &model.Payment{
		Name:         req.Name,
		Method:       req.Method,
		Config:       req.Config,
		Enable:       req.Enable,
		SortOrder:    req.SortOrder,
		NotifyDomain: req.NotifyDomain,
		UUID:         uuid.New().String(),
	}

	if err := payment.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(payment).Error; err != nil {
		return nil, err
	}

	return s.toPaymentMethodConfig(payment), nil
}

// UpdatePaymentMethod 更新支付方式
func (s *PaymentService) UpdatePaymentMethod(id uint, req *PaymentConfigRequest) (*PaymentMethodConfig, error) {
	payment, err := s.GetPaymentMethodByID(id)
	if err != nil {
		return nil, err
	}

	// 检查支付方式标识是否重复
	if req.Method != payment.Method {
		var existingPayment model.Payment
		if err := s.db.Where("method = ? AND id != ?", req.Method, id).First(&existingPayment).Error; err == nil {
			return nil, errors.New("支付方式标识已存在")
		}
	}

	// 更新支付方式
	payment.Name = req.Name
	payment.Method = req.Method
	payment.Config = req.Config
	payment.Enable = req.Enable
	payment.SortOrder = req.SortOrder
	payment.NotifyDomain = req.NotifyDomain

	if err := payment.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(payment).Error; err != nil {
		return nil, err
	}

	return s.toPaymentMethodConfig(payment), nil
}

// DeletePaymentMethod 删除支付方式
func (s *PaymentService) DeletePaymentMethod(id uint) error {
	payment, err := s.GetPaymentMethodByID(id)
	if err != nil {
		return err
	}

	return s.db.Delete(payment).Error
}

// GetPaymentMethodByID 根据ID获取支付方式
func (s *PaymentService) GetPaymentMethodByID(id uint) (*model.Payment, error) {
	var payment model.Payment
	if err := s.db.First(&payment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("支付方式不存在")
		}
		return nil, err
	}
	return &payment, nil
}

// GetPaymentForm 获取支付方式配置表单
func (s *PaymentService) GetPaymentForm(method string) (map[string]FormField, error) {
	gateway, err := s.createPaymentGateway(method, nil)
	if err != nil {
		return nil, err
	}

	return gateway.Form(), nil
}

// CreatePayment 创建支付订单
func (s *PaymentService) CreatePayment(method string, order *model.Order) (*PaymentResult, error) {
	payment, err := s.GetPaymentMethod(method)
	if err != nil {
		return nil, err
	}

	gateway, err := s.createPaymentGateway(method, payment)
	if err != nil {
		return nil, err
	}

	// 构建支付订单
	paymentOrder := PaymentOrder{
		OrderID:     order.OrderID,
		TotalAmount: order.Amount,
		UserID:      order.UserID,
		Subject:     fmt.Sprintf("订单 %s", order.OrderID),
		Body:        fmt.Sprintf("商品：%s", order.Product.Name),
	}

	// 设置通知URL
	notifyURL := fmt.Sprintf("/api/v1/guest/payment/notify/%s/%s", method, payment.UUID)
	if payment.NotifyDomain != "" {
		notifyURL = payment.NotifyDomain + "/api/v1/guest/payment/notify/" + method + "/" + payment.UUID
	}
	paymentOrder.NotifyURL = notifyURL

	// 发起支付
	result, err := gateway.Pay(paymentOrder)
	if err != nil {
		return nil, err
	}

	// 记录支付日志
	paymentLog := &model.PaymentLog{
		OrderID:      order.OrderID,
		PaymentID:    result.TransactionID,
		Method:       method,
		Amount:       order.Amount,
		Status:       "pending",
		CallbackData: map[string]interface{}{
			"payment_result": result,
		},
	}

	if err := s.db.Create(paymentLog).Error; err != nil {
		// 即使日志记录失败，也返回支付结果
		return result, nil
	}

	return result, nil
}

// ProcessPaymentNotify 处理支付回调
func (s *PaymentService) ProcessPaymentNotify(method, uuid string, params map[string]interface{}) (*PaymentNotifyResult, error) {
	payment, err := s.GetPaymentMethodByUUID(uuid)
	if err != nil {
		return nil, err
	}

	if !payment.Enable {
		return nil, errors.New("支付方式已禁用")
	}

	gateway, err := s.createPaymentGateway(method, payment)
	if err != nil {
		return nil, err
	}

	// 处理支付回调
	result, err := gateway.Notify(params)
	if err != nil {
		return nil, err
	}

	// 记录支付日志
	paymentLog := &model.PaymentLog{
		OrderID:      result.TradeNo,
		PaymentID:    result.CallbackNo,
		Method:       method,
		Amount:       parseFloat(result.Amount),
		Status:       result.Status,
		CallbackData: params,
	}

	if err := s.db.Create(paymentLog).Error; err != nil {
		// 即使日志记录失败，也返回回调结果
		return result, nil
	}

	return result, nil
}

// QueryPaymentLogs 查询支付日志
func (s *PaymentService) QueryPaymentLogs(req *PaymentLogQueryRequest) (*PaymentLogQueryResponse, error) {
	query := s.db.Model(&model.PaymentLog{})

	// 订单ID筛选
	if req.OrderID != "" {
		query = query.Where("order_id = ?", req.OrderID)
	}

	// 支付方式筛选
	if req.Method != "" {
		query = query.Where("method = ?", req.Method)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 时间范围筛选
	if req.StartDate != "" {
		query = query.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		query = query.Where("created_at <= ?", req.EndDate)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	query = query.Order("created_at DESC")

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询支付日志
	var paymentLogs []model.PaymentLog
	if err := query.Find(&paymentLogs).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	paymentLogDTOs := make([]*PaymentLogDTO, len(paymentLogs))
	for i, log := range paymentLogs {
		paymentLogDTOs[i] = s.toPaymentLogDTO(&log)
	}

	return &PaymentLogQueryResponse{
		Total:       total,
		PaymentLogs: paymentLogDTOs,
	}, nil
}

// ProcessRefund 处理退款
func (s *PaymentService) ProcessRefund(method string, refundOrder PaymentRefundOrder) (*PaymentRefundResult, error) {
	payment, err := s.GetPaymentMethod(method)
	if err != nil {
		return nil, err
	}

	gateway, err := s.createPaymentGateway(method, payment)
	if err != nil {
		return nil, err
	}

	// 处理退款
	result, err := gateway.Refund(refundOrder)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetPaymentStats 获取支付统计
func (s *PaymentService) GetPaymentStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总支付方式数
	var totalPaymentMethods int64
	if err := s.db.Model(&model.Payment{}).Where("enable = ?", true).Count(&totalPaymentMethods).Error; err != nil {
		return nil, err
	}
	stats["total_payment_methods"] = totalPaymentMethods

	// 今日支付成功数
	var todaySuccessCount int64
	if err := s.db.Model(&model.PaymentLog{}).
		Where("status = ? AND DATE(created_at) = CURDATE()", "success").
		Count(&todaySuccessCount).Error; err != nil {
		return nil, err
	}
	stats["today_success_count"] = todaySuccessCount

	// 今日支付金额
	var todayAmount float64
	if err := s.db.Model(&model.PaymentLog{}).
		Where("status = ? AND DATE(created_at) = CURDATE()", "success").
		Select("COALESCE(SUM(amount), 0)").Scan(&todayAmount).Error; err != nil {
		return nil, err
	}
	stats["today_amount"] = todayAmount

	// 本月支付成功数
	var monthSuccessCount int64
	if err := s.db.Model(&model.PaymentLog{}).
		Where("status = ? AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())", "success").
		Count(&monthSuccessCount).Error; err != nil {
		return nil, err
	}
	stats["month_success_count"] = monthSuccessCount

	// 本月支付金额
	var monthAmount float64
	if err := s.db.Model(&model.PaymentLog{}).
		Where("status = ? AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())", "success").
		Select("COALESCE(SUM(amount), 0)").Scan(&monthAmount).Error; err != nil {
		return nil, err
	}
	stats["month_amount"] = monthAmount

	return stats, nil
}

// 辅助方法

// toPaymentMethodConfig 转换为支付方式配置DTO
func (s *PaymentService) toPaymentMethodConfig(payment *model.Payment) *PaymentMethodConfig {
	config := make(map[string]interface{})
	if payment.Config != nil {
		_ = json.Unmarshal(payment.Config, &config)
	}

	return &PaymentMethodConfig{
		ID:           payment.ID,
		Name:         payment.Name,
		Method:       payment.Method,
		Config:       config,
		Enable:       payment.Enable,
		SortOrder:    payment.SortOrder,
		NotifyDomain: payment.NotifyDomain,
		UUID:         payment.UUID,
	}
}

// toPaymentLogDTO 转换为支付日志DTO
func (s *PaymentService) toPaymentLogDTO(log *model.PaymentLog) *PaymentLogDTO {
	callbackData := make(map[string]interface{})
	if log.CallbackData != nil {
		_ = json.Unmarshal(log.CallbackData, &callbackData)
	}

	return &PaymentLogDTO{
		ID:           log.ID,
		OrderID:      log.OrderID,
		PaymentID:    log.PaymentID,
		Method:       log.Method,
		Amount:       log.Amount,
		Status:       log.Status,
		CallbackData: callbackData,
		ErrorMessage: log.ErrorMessage,
		CreatedAt:    log.CreatedAt.Format("2006-01-02 15:04:05"),
	}
}

// ToPaymentMethodConfig 转换为支付方式配置DTO
func (s *PaymentService) ToPaymentMethodConfig(payment *model.Payment) *PaymentMethodConfig {
	config := make(map[string]interface{})
	if payment.Config != nil {
		_ = json.Unmarshal(payment.Config, &config)
	}

	return &PaymentMethodConfig{
		ID:           payment.ID,
		Name:         payment.Name,
		Method:       payment.Method,
		Config:       config,
		Enable:       payment.Enable,
		SortOrder:    payment.SortOrder,
		NotifyDomain: payment.NotifyDomain,
		UUID:         payment.UUID,
	}
}

// createPaymentGateway 创建支付网关实例
func (s *PaymentService) createPaymentGateway(method string, payment *model.Payment) (PaymentGateway, error) {
	switch method {
	case "alipay_f2f":
		return NewAlipayF2FGateway(payment), nil
	case "wechat_pay_native":
		return NewWechatPayNativeGateway(payment), nil
	default:
		return nil, fmt.Errorf("不支持的支付方式: %s", method)
	}
}

// parseFloat 安全转换字符串为float64
func parseFloat(s string) float64 {
	var result float64
	_, err := fmt.Sscanf(s, "%f", &result)
	if err != nil {
		return 0
	}
	return result
}