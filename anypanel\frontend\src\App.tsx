import { Routes, Route } from 'react-router-dom'
import { Layout } from 'antd'

const App: React.FC = () => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Layout.Header style={{ background: '#fff', padding: '0 24px' }}>
        <h1 style={{ margin: 0, color: '#1890ff' }}>AnyTLS Panel</h1>
      </Layout.Header>
      <Layout.Content style={{ padding: '24px' }}>
        <Routes>
          <Route path="/" element={<div>欢迎使用 AnyTLS Panel</div>} />
          <Route path="/admin" element={<div>管理员界面</div>} />
          <Route path="/user" element={<div>用户界面</div>} />
        </Routes>
      </Layout.Content>
    </Layout>
  )
}

export default App