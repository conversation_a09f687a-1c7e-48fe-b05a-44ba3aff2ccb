package api

import (
	"anypanel/internal/middleware"
	"anypanel/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PermissionGroupHandler 权限组处理器
type PermissionGroupHandler struct {
	permissionGroupService *service.PermissionGroupService
}

func NewPermissionGroupHandler(db *gorm.DB) *PermissionGroupHandler {
	return &PermissionGroupHandler{
		permissionGroupService: service.NewPermissionGroupService(db),
	}
}

// GetPermissionGroupByID 根据ID获取权限组
func (h *PermissionGroupHandler) GetPermissionGroupByID(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的权限组ID",
			"message": err.Error(),
		})
		return
	}

	group, err := h.permissionGroupService.GetPermissionGroupWithDetails(uint(groupID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "获取权限组失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    group,
	})
}

// GetPermissionGroups 获取权限组列表
func (h *PermissionGroupHandler) GetPermissionGroups(c *gin.Context) {
	var req service.PermissionGroupQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	resp, err := h.permissionGroupService.QueryPermissionGroups(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查询权限组失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "查询成功",
		"data":    resp,
	})
}

// GetAllPermissionGroups 获取所有权限组
func (h *PermissionGroupHandler) GetAllPermissionGroups(c *gin.Context) {
	groups, err := h.permissionGroupService.GetAllPermissionGroups()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取权限组失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    groups,
	})
}

// CreatePermissionGroup 创建权限组
func (h *PermissionGroupHandler) CreatePermissionGroup(c *gin.Context) {
	var req service.PermissionGroupCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	group, err := h.permissionGroupService.CreatePermissionGroup(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "创建权限组失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    group,
	})
}

// UpdatePermissionGroup 更新权限组
func (h *PermissionGroupHandler) UpdatePermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的权限组ID",
			"message": err.Error(),
		})
		return
	}

	var req service.PermissionGroupUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	group, err := h.permissionGroupService.UpdatePermissionGroup(uint(groupID), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "更新权限组失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
		"data":    group,
	})
}

// DeletePermissionGroup 删除权限组
func (h *PermissionGroupHandler) DeletePermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的权限组ID",
			"message": err.Error(),
		})
		return
	}

	if err := h.permissionGroupService.DeletePermissionGroup(uint(groupID)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "删除权限组失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// GetPermissionGroupNodes 获取权限组关联的节点
func (h *PermissionGroupHandler) GetPermissionGroupNodes(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的权限组ID",
			"message": err.Error(),
		})
		return
	}

	nodes, err := h.permissionGroupService.GetPermissionGroupNodes(uint(groupID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取权限组节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    nodes,
	})
}

// AddNodeToPermissionGroup 向权限组添加节点
func (h *PermissionGroupHandler) AddNodeToPermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的权限组ID",
			"message": err.Error(),
		})
		return
	}

	var req struct {
		NodeID uint `json:"node_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	if err := h.permissionGroupService.AddNodeToPermissionGroup(uint(groupID), req.NodeID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "添加节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "添加成功",
	})
}

// RemoveNodeFromPermissionGroup 从权限组移除节点
func (h *PermissionGroupHandler) RemoveNodeFromPermissionGroup(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的权限组ID",
			"message": err.Error(),
		})
		return
	}

	var req struct {
		NodeID uint `json:"node_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	if err := h.permissionGroupService.RemoveNodeFromPermissionGroup(uint(groupID), req.NodeID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "移除节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "移除成功",
	})
}

// SetPermissionGroupNodes 设置权限组的节点（批量替换）
func (h *PermissionGroupHandler) SetPermissionGroupNodes(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的权限组ID",
			"message": err.Error(),
		})
		return
	}

	var req struct {
		NodeIDs []uint `json:"node_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	if err := h.permissionGroupService.SetPermissionGroupNodes(uint(groupID), req.NodeIDs); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "设置节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "设置成功",
	})
}

// GetPermissionGroupProducts 获取权限组关联的商品
func (h *PermissionGroupHandler) GetPermissionGroupProducts(c *gin.Context) {
	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的权限组ID",
			"message": err.Error(),
		})
		return
	}

	products, err := h.permissionGroupService.GetPermissionGroupProducts(uint(groupID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取权限组商品失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    products,
	})
}

// GetUserPermissionGroups 获取用户的权限组
func (h *PermissionGroupHandler) GetUserPermissionGroups(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	groups, err := h.permissionGroupService.GetUserPermissionGroups(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取用户权限组失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    groups,
	})
}

// CheckUserNodePermission 检查用户是否有权限访问节点
func (h *PermissionGroupHandler) CheckUserNodePermission(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	nodeIDStr := c.Param("node_id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的节点ID",
			"message": err.Error(),
		})
		return
	}

	hasPermission, err := h.permissionGroupService.CheckUserNodePermission(uint(userID), uint(nodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "检查权限失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "检查成功",
		"data": gin.H{
			"has_permission": hasPermission,
		},
	})
}

// GetUserNodes 获取用户有权限访问的节点
func (h *PermissionGroupHandler) GetUserNodes(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	nodes, err := h.permissionGroupService.GetUserNodes(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取用户节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    nodes,
	})
}

// SetupPermissionGroupRoutes 设置权限组路由
func SetupPermissionGroupRoutes(router *gin.RouterGroup, db *gorm.DB) {
	permissionGroupHandler := NewPermissionGroupHandler(db)

	groups := router.Group("/permission-groups")
	{
		// 需要认证的路由
		groups.Use(middleware.AuthMiddleware(db))
		{
			// 管理员权限路由
			admin := groups.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				admin.GET("", permissionGroupHandler.GetPermissionGroups)
				admin.GET("/all", permissionGroupHandler.GetAllPermissionGroups)
				admin.POST("", permissionGroupHandler.CreatePermissionGroup)
				admin.GET("/:id", permissionGroupHandler.GetPermissionGroupByID)
				admin.PUT("/:id", permissionGroupHandler.UpdatePermissionGroup)
				admin.DELETE("/:id", permissionGroupHandler.DeletePermissionGroup)
				admin.GET("/:id/nodes", permissionGroupHandler.GetPermissionGroupNodes)
				admin.POST("/:id/nodes", permissionGroupHandler.AddNodeToPermissionGroup)
				admin.DELETE("/:id/nodes", permissionGroupHandler.RemoveNodeFromPermissionGroup)
				admin.PUT("/:id/nodes", permissionGroupHandler.SetPermissionGroupNodes)
				admin.GET("/:id/products", permissionGroupHandler.GetPermissionGroupProducts)
				admin.GET("/users/:user_id", permissionGroupHandler.GetUserPermissionGroups)
				admin.GET("/users/:user_id/nodes/:node_id", permissionGroupHandler.CheckUserNodePermission)
				admin.GET("/users/:user_id/nodes", permissionGroupHandler.GetUserNodes)
			}
		}
	}
}