.PHONY: help dev build clean install-deps start-db stop-db reset-db

# 默认目标
help:
	@echo "可用的命令："
	@echo "  dev         - 启动开发环境"
	@echo "  build       - 构建项目"
	@echo "  clean       - 清理构建文件"
	@echo "  install-deps - 安装依赖"
	@echo "  start-db    - 启动数据库"
	@echo "  stop-db     - 停止数据库"
	@echo "  reset-db    - 重置数据库"

# 启动开发环境
dev:
	@echo "🚀 启动开发环境..."
	@mkdir -p logs
	@docker-compose up -d mysql redis
	@echo "⏳ 等待数据库启动..."
	@sleep 10
	@docker-compose exec mysql mysql -uroot -prootpassword anypanel < scripts/init.sql
	@echo "🔧 启动后端服务..."
	@cd backend && go run cmd/main.go &
	@echo "🎨 启动前端服务..."
	@cd frontend && npm run dev &
	@echo "✅ 开发环境启动完成！"

# 构建项目
build:
	@echo "🔨 构建项目..."
	@cd frontend && npm run build
	@echo "📦 构建后端..."
	@cd backend && go build -o ../bin/anypanel ./cmd/main.go
	@echo "✅ 构建完成！"

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	@rm -rf frontend/dist
	@rm -rf backend/tmp
	@rm -rf bin/*
	@rm -rf logs/*
	@echo "✅ 清理完成！"

# 安装依赖
install-deps:
	@echo "📦 安装前端依赖..."
	@cd frontend && npm install
	@echo "📦 安装后端依赖..."
	@cd backend && go mod tidy
	@echo "✅ 依赖安装完成！"

# 启动数据库
start-db:
	@echo "🗄️ 启动数据库..."
	@docker-compose up -d mysql redis
	@echo "⏳ 等待数据库启动..."
	@sleep 10
	@docker-compose exec mysql mysql -uroot -prootpassword anypanel < scripts/init.sql
	@echo "✅ 数据库启动完成！"

# 停止数据库
stop-db:
	@echo "🛑 停止数据库..."
	@docker-compose down
	@echo "✅ 数据库已停止！"

# 重置数据库
reset-db:
	@echo "🔄 重置数据库..."
	@docker-compose exec mysql mysql -uroot -prootpassword -e "DROP DATABASE IF EXISTS anypanel; CREATE DATABASE anypanel;"
	@docker-compose exec mysql mysql -uroot -prootpassword anypanel < scripts/init.sql
	@echo "✅ 数据库重置完成！"

# 安装Air (热重载工具)
install-air:
	@echo "🔧 安装Air..."
	@go install github.com/cosmtrek/air@latest
	@echo "✅ Air安装完成！"

# 使用Air启动后端热重载
dev-backend:
	@echo "🔧 启动后端热重载..."
	@cd backend && air -c ../.air.toml

# 生产环境部署
deploy:
	@echo "🚀 部署到生产环境..."
	@docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
	@echo "✅ 部署完成！"