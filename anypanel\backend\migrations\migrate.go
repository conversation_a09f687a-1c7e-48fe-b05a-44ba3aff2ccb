package migrate

import (
	"anypanel/internal/model"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

// Migration 数据库迁移结构体
type Migration struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"uniqueIndex;size:255"`
	Batch     int       `json:"batch"`
	CreatedAt time.Time `json:"created_at"`
}

// Migrator 数据库迁移器
type Migrator struct {
	db *gorm.DB
}

// NewMigrator 创建新的迁移器
func NewMigrator(db *gorm.DB) *Migrator {
	return &Migrator{db: db}
}

// Run 执行所有迁移
func (m *Migrator) Run() error {
	log.Println("🔄 开始执行数据库迁移...")

	// 创建迁移表
	if err := m.db.AutoMigrate(&Migration{}); err != nil {
		return fmt.Errorf("创建迁移表失败: %w", err)
	}

	// 执行迁移
	migrations := []struct {
		name string
		fn   func(*gorm.DB) error
	}{
		{"create_users_table", m.createUsersTable},
		{"create_nodes_table", m.createNodesTable},
		{"create_permission_groups_table", m.createPermissionGroupsTable},
		{"create_products_table", m.createProductsTable},
		{"create_orders_table", m.createOrdersTable},
		{"create_user_subscriptions_table", m.createUserSubscriptionsTable},
		{"create_traffic_logs_table", m.createTrafficLogsTable},
		{"create_online_users_table", m.createOnlineUsersTable},
		{"create_user_nodes_table", m.createUserNodesTable},
		{"create_permission_group_nodes_table", m.createPermissionGroupNodesTable},
		{"create_indexes", m.createIndexes},
	}

	for _, migration := range migrations {
		if err := m.runMigration(migration.name, migration.fn); err != nil {
			return err
		}
	}

	log.Println("✅ 数据库迁移完成")
	return nil
}

// runMigration 执行单个迁移
func (m *Migrator) runMigration(name string, fn func(*gorm.DB) error) error {
	// 检查迁移是否已经执行过
	var count int64
	if err := m.db.Model(&Migration{}).Where("name = ?", name).Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		log.Printf("✅ 迁移 %s 已经执行过，跳过", name)
		return nil
	}

	log.Printf("🔄 执行迁移: %s", name)

	// 开始事务
	tx := m.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 执行迁移
	if err := fn(tx); err != nil {
		tx.Rollback()
		return fmt.Errorf("迁移 %s 失败: %w", name, err)
	}

	// 记录迁移
	if err := tx.Create(&Migration{
		Name:      name,
		Batch:     m.getNextBatchNumber(),
		CreatedAt: time.Now(),
	}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("记录迁移 %s 失败: %w", name, err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交迁移 %s 失败: %w", name, err)
	}

	log.Printf("✅ 迁移 %s 执行成功", name)
	return nil
}

// getNextBatchNumber 获取下一个批次号
func (m *Migrator) getNextBatchNumber() int {
	var maxBatch int
	m.db.Model(&Migration{}).Select("COALESCE(MAX(batch), 0)").Scan(&maxBatch)
	return maxBatch + 1
}

// createUsersTable 创建用户表
func (m *Migrator) createUsersTable(db *gorm.DB) error {
	return db.AutoMigrate(&model.User{})
}

// createNodesTable 创建节点表
func (m *Migrator) createNodesTable(db *gorm.DB) error {
	return db.AutoMigrate(&model.Node{})
}

// createPermissionGroupsTable 创建权限组表
func (m *Migrator) createPermissionGroupsTable(db *gorm.DB) error {
	return db.AutoMigrate(&model.PermissionGroup{})
}

// createProductsTable 创建商品表
func (m *Migrator) createProductsTable(db *gorm.DB) error {
	return db.AutoMigrate(&model.Product{})
}

// createOrdersTable 创建订单表
func (m *Migrator) createOrdersTable(db *gorm.DB) error {
	return db.AutoMigrate(&model.Order{})
}

// createUserSubscriptionsTable 创建用户订阅表
func (m *Migrator) createUserSubscriptionsTable(db *gorm.DB) error {
	return db.AutoMigrate(&model.UserSubscription{})
}

// createTrafficLogsTable 创建流量日志表
func (m *Migrator) createTrafficLogsTable(db *gorm.DB) error {
	return db.AutoMigrate(&model.TrafficLog{})
}

// createOnlineUsersTable 创建在线用户表
func (m *Migrator) createOnlineUsersTable(db *gorm.DB) error {
	return db.AutoMigrate(&model.OnlineUser{})
}

// createUserNodesTable 创建用户节点关联表
func (m *Migrator) createUserNodesTable(db *gorm.DB) error {
	return db.Exec(`
		CREATE TABLE IF NOT EXISTS user_nodes (
			user_id INT UNSIGNED NOT NULL,
			node_id INT UNSIGNED NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (user_id, node_id),
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
			FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE
		)
	`).Error
}

// createPermissionGroupNodesTable 创建权限组节点关联表
func (m *Migrator) createPermissionGroupNodesTable(db *gorm.DB) error {
	return db.Exec(`
		CREATE TABLE IF NOT EXISTS permission_group_nodes (
			permission_group_id INT UNSIGNED NOT NULL,
			node_id INT UNSIGNED NOT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (permission_group_id, node_id),
			FOREIGN KEY (permission_group_id) REFERENCES permission_groups(id) ON DELETE CASCADE,
			FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE
		)
	`).Error
}

// createIndexes 创建索引
func (m *Migrator) createIndexes(db *gorm.DB) error {
	// 用户表索引
	indexes := []struct {
		table    string
		name     string
		columns  string
		unique   bool
		ifNotExists bool
	}{
		{"users", "idx_users_email", "email", true, true},
		{"users", "idx_users_username", "username", true, true},
		{"users", "idx_users_uuid", "uuid", true, true},
		{"users", "idx_users_status", "status", false, true},
		{"users", "idx_users_role", "role", false, true},
		{"users", "idx_users_expired_at", "expired_at", false, true},
		
		{"nodes", "idx_nodes_protocol", "protocol", false, true},
		{"nodes", "idx_nodes_status", "status", false, true},
		{"nodes", "idx_nodes_host_port", "host, port", false, true},
		
		{"permission_groups", "idx_permission_groups_name", "name", true, true},
		{"permission_groups", "idx_permission_groups_sort_order", "sort_order", false, true},
		
		{"products", "idx_products_permission_group_id", "permission_group_id", false, true},
		{"products", "idx_products_status", "status", false, true},
		{"products", "idx_products_sort_order", "sort_order", false, true},
		
		{"orders", "idx_orders_order_id", "order_id", true, true},
		{"orders", "idx_orders_user_id", "user_id", false, true},
		{"orders", "idx_orders_product_id", "product_id", false, true},
		{"orders", "idx_orders_status", "status", false, true},
		{"orders", "idx_orders_created_at", "created_at", false, true},
		
		{"user_subscriptions", "idx_user_subscriptions_user_id", "user_id", false, true},
		{"user_subscriptions", "idx_user_subscriptions_product_id", "product_id", false, true},
		{"user_subscriptions", "idx_user_subscriptions_status", "status", false, true},
		{"user_subscriptions", "idx_user_subscriptions_expired_at", "expired_at", false, true},
		
		{"traffic_logs", "idx_traffic_logs_user_id", "user_id", false, true},
		{"traffic_logs", "idx_traffic_logs_node_id", "node_id", false, true},
		{"traffic_logs", "idx_traffic_logs_recorded_at", "recorded_at", false, true},
		
		{"online_users", "idx_online_users_user_id", "user_id", false, true},
		{"online_users", "idx_online_users_node_id", "node_id", false, true},
		{"online_users", "idx_online_users_ip_address", "ip_address", false, true},
		{"online_users", "idx_online_users_last_seen", "last_seen", false, true},
	}

	for _, idx := range indexes {
		var sql string
		if idx.ifNotExists {
			sql = fmt.Sprintf("CREATE %s INDEX IF NOT EXISTS %s ON %s (%s)",
				map[bool]string{true: "UNIQUE", false: ""}[idx.unique],
				idx.name, idx.table, idx.columns)
		} else {
			sql = fmt.Sprintf("CREATE %s INDEX %s ON %s (%s)",
				map[bool]string{true: "UNIQUE", false: ""}[idx.unique],
				idx.name, idx.table, idx.columns)
		}
		
		if err := db.Exec(sql).Error; err != nil {
			log.Printf("⚠️ 创建索引 %s 失败（可能已存在）: %v", idx.name, err)
		}
	}

	return nil
}