package api

import (
	"anypanel/internal/middleware"
	"anypanel/internal/service"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// NodeHandler 节点处理器
type NodeHandler struct {
	nodeService *service.NodeService
}

func NewNodeHandler(db *gorm.DB) *NodeHandler {
	return &NodeHandler{
		nodeService: service.NewNodeService(db),
	}
}

// GetNodeByID 根据ID获取节点
func (h *NodeHandler) GetNodeByID(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的节点ID",
			"message": err.Error(),
		})
		return
	}

	node, err := h.nodeService.GetNodeWithDetails(uint(nodeID))
	if err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{
			"error":   "获取节点失败",
			"message": err.<PERSON>rror(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    node,
	})
}

// GetNodes 获取节点列表
func (h *NodeHandler) GetNodes(c *gin.Context) {
	var req service.NodeQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	resp, err := h.nodeService.QueryNodes(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查询节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "查询成功",
		"data":    resp,
	})
}

// GetAllNodes 获取所有节点
func (h *NodeHandler) GetAllNodes(c *gin.Context) {
	nodes, err := h.nodeService.GetAllNodes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    nodes,
	})
}

// GetOnlineNodes 获取在线节点
func (h *NodeHandler) GetOnlineNodes(c *gin.Context) {
	nodes, err := h.nodeService.GetOnlineNodes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取在线节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    nodes,
	})
}

// CreateNode 创建节点
func (h *NodeHandler) CreateNode(c *gin.Context) {
	var req service.NodeCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	node, err := h.nodeService.CreateNode(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "创建节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    node,
	})
}

// UpdateNode 更新节点
func (h *NodeHandler) UpdateNode(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的节点ID",
			"message": err.Error(),
		})
		return
	}

	var req service.NodeUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	node, err := h.nodeService.UpdateNode(uint(nodeID), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "更新节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
		"data":    node,
	})
}

// DeleteNode 删除节点
func (h *NodeHandler) DeleteNode(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的节点ID",
			"message": err.Error(),
		})
		return
	}

	if err := h.nodeService.DeleteNode(uint(nodeID)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "删除节点失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// UpdateNodeStatus 更新节点状态
func (h *NodeHandler) UpdateNodeStatus(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的节点ID",
			"message": err.Error(),
		})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required,oneof=online offline maintenance"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	if err := h.nodeService.UpdateNodeStatus(uint(nodeID), req.Status); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "更新节点状态失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
	})
}

// GetNodeStats 获取节点统计信息
func (h *NodeHandler) GetNodeStats(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的节点ID",
			"message": err.Error(),
		})
		return
	}

	stats, err := h.nodeService.GetNodeStats(uint(nodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取节点统计失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    stats,
	})
}

// GetAllNodesStats 获取所有节点统计信息
func (h *NodeHandler) GetAllNodesStats(c *gin.Context) {
	stats, err := h.nodeService.GetAllNodesStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取节点统计失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    stats,
	})
}

// HealthCheck 节点健康检查
func (h *NodeHandler) HealthCheck(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的节点ID",
			"message": err.Error(),
		})
		return
	}

	result, err := h.nodeService.HealthCheck(uint(nodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "健康检查失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "检查成功",
		"data":    result,
	})
}

// BatchHealthCheck 批量节点健康检查
func (h *NodeHandler) BatchHealthCheck(c *gin.Context) {
	results, err := h.nodeService.BatchHealthCheck()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "批量健康检查失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "检查成功",
		"data":    results,
	})
}

// GetNodeGroups 获取节点关联的权限组
func (h *NodeHandler) GetNodeGroups(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的节点ID",
			"message": err.Error(),
		})
		return
	}

	groups, err := h.nodeService.GetNodeGroups(uint(nodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取节点权限组失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    groups,
	})
}

// BatchUpdateNodes 批量更新节点
func (h *NodeHandler) BatchUpdateNodes(c *gin.Context) {
	var req struct {
		NodeIDs []uint `json:"node_ids" binding:"required,min=1"`
		Action  string `json:"action" binding:"required,oneof=enable disable maintain delete"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	var successCount int
	var errors []string

	for _, nodeID := range req.NodeIDs {
		var err error
		switch req.Action {
		case "enable":
			err = h.nodeService.UpdateNodeStatus(nodeID, "online")
		case "disable":
			err = h.nodeService.UpdateNodeStatus(nodeID, "offline")
		case "maintain":
			err = h.nodeService.UpdateNodeStatus(nodeID, "maintenance")
		case "delete":
			err = h.nodeService.DeleteNode(nodeID)
		}

		if err != nil {
			errors = append(errors, fmt.Sprintf("节点ID %d: %v", nodeID, err))
		} else {
			successCount++
		}
	}

	response := gin.H{
		"success": true,
		"message": fmt.Sprintf("批量操作完成，成功: %d, 失败: %d", successCount, len(errors)),
		"data": gin.H{
			"success_count": successCount,
			"failed_count":  len(errors),
			"errors":        errors,
		},
	}

	c.JSON(http.StatusOK, response)
}

// SetupNodeRoutes 设置节点路由
func SetupNodeRoutes(router *gin.RouterGroup, db *gorm.DB) {
	nodeHandler := NewNodeHandler(db)

	nodes := router.Group("/nodes")
	{
		// 需要认证的路由
		nodes.Use(middleware.AuthMiddleware(db))
		{
			// 管理员权限路由
			admin := nodes.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				admin.GET("", nodeHandler.GetNodes)
				admin.GET("/all", nodeHandler.GetAllNodes)
				admin.GET("/online", nodeHandler.GetOnlineNodes)
				admin.POST("", nodeHandler.CreateNode)
				admin.GET("/:id", nodeHandler.GetNodeByID)
				admin.PUT("/:id", nodeHandler.UpdateNode)
				admin.DELETE("/:id", nodeHandler.DeleteNode)
				admin.PUT("/:id/status", nodeHandler.UpdateNodeStatus)
				admin.GET("/:id/stats", nodeHandler.GetNodeStats)
				admin.GET("/:id/health", nodeHandler.HealthCheck)
				admin.GET("/:id/groups", nodeHandler.GetNodeGroups)
				admin.GET("/stats", nodeHandler.GetAllNodesStats)
				admin.POST("/health-check", nodeHandler.BatchHealthCheck)
				admin.POST("/batch", nodeHandler.BatchUpdateNodes)
			}
		}
	}
}