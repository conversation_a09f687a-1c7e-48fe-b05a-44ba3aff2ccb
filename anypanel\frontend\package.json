{"name": "anytls-panel-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/pro-components": "^2.6.43", "@ant-design/pro-layout": "^7.17.16", "@ant-design/charts": "^2.0.3", "@tanstack/react-query": "^5.17.0", "antd": "^5.12.8", "axios": "^1.6.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "dayjs": "^1.11.10", "lodash": "^4.17.21", "qrcode": "^1.5.3"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/node": "^20.10.4", "@types/lodash": "^4.14.202", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.54.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.3.3", "vite": "^5.0.8"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}