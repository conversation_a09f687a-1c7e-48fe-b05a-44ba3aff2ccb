package api

import (
	"anypanel/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type V2bXAPI struct {
	v2bxService *service.V2bXService
}

func NewV2bXAPI(db *gorm.DB) *V2bXAPI {
	nodeService := service.NewNodeService(db)
	userService := service.NewUserService(db)
	v2bxService := service.NewV2bXService(db, nodeService, userService)
	
	return &V2bXAPI{
		v2bxService: v2bxService,
	}
}

// SetupV2bXRoutes 设置V2bX兼容API路由
func SetupV2bXRoutes(router *gin.RouterGroup, db *gorm.DB) {
	api := NewV2bXAPI(db)
	
	// V2bX UniProxy API路由组
	v2bx := router.Group("/v1/server/UniProxy")
	{
		// 节点配置接口
		v2bx.GET("/config", api.GetNodeConfig)
		
		// 用户管理接口
		v2bx.GET("/user", api.GetNodeUsers)
		
		// 流量上报接口
		v2bx.POST("/push", api.ReportTraffic)
		
		// 在线用户接口
		v2bx.POST("/alive", api.ReportAlive)
		v2bx.GET("/alivelist", api.GetAliveList)
	}
}

// GetNodeConfig 获取节点配置
func (api *V2bXAPI) GetNodeConfig(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		token = c.GetHeader("X-Node-Token")
		if token == "" {
			// 尝试从Authorization header获取
			auth := c.GetHeader("Authorization")
			if len(auth) > 7 && auth[:7] == "Bearer " {
				token = auth[7:]
			}
		}
	}

	config, err := api.v2bxService.GetNodeConfig(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "获取配置失败",
			"error":   err.Error(),
		})
		return
	}

	// 支持ETag缓存
	etag := c.GetHeader("If-None-Match")
	if etag != "" {
		// 简单的ETag验证，实际应该根据配置内容生成
		c.JSON(http.StatusNotModified, gin.H{
			"success": true,
			"message": "配置未变化",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取配置成功",
		"data":    config,
	})
}

// GetNodeUsers 获取节点用户列表
func (api *V2bXAPI) GetNodeUsers(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		token = c.GetHeader("X-Node-Token")
		if token == "" {
			auth := c.GetHeader("Authorization")
			if len(auth) > 7 && auth[:7] == "Bearer " {
				token = auth[7:]
			}
		}
	}

	// 支持msgpack格式
	accept := c.GetHeader("Accept")
	
	userResponse, err := api.v2bxService.GetNodeUsers(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "获取用户列表失败",
			"error":   err.Error(),
		})
		return
	}

	// 根据Accept头返回不同格式
	if accept == "application/msgpack" {
		// 这里应该返回msgpack格式，简化处理返回JSON
		c.Header("Content-Type", "application/json")
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户列表成功",
		"data":    userResponse,
	})
}

// ReportTraffic 处理流量上报
func (api *V2bXAPI) ReportTraffic(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		token = c.GetHeader("X-Node-Token")
		if token == "" {
			auth := c.GetHeader("Authorization")
			if len(auth) > 7 && auth[:7] == "Bearer " {
				token = auth[7:]
			}
		}
	}

	// 解析流量数据
	var reports []service.TrafficReport
	contentType := c.GetHeader("Content-Type")
	
	if contentType == "application/msgpack" {
		// 这里应该解析msgpack格式，简化处理
		if err := c.ShouldBindJSON(&reports); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "解析流量数据失败",
				"error":   err.Error(),
			})
			return
		}
	} else {
		// JSON格式
		if err := c.ShouldBindJSON(&reports); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "解析流量数据失败",
				"error":   err.Error(),
			})
			return
		}
	}

	err := api.v2bxService.ReportTraffic(token, reports)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "处理流量上报失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "流量上报处理成功",
	})
}

// ReportAlive 处理在线用户上报
func (api *V2bXAPI) ReportAlive(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		token = c.GetHeader("X-Node-Token")
		if token == "" {
			auth := c.GetHeader("Authorization")
			if len(auth) > 7 && auth[:7] == "Bearer " {
				token = auth[7:]
			}
		}
	}

	// 解析在线用户数据
	var reports []service.AliveReport
	contentType := c.GetHeader("Content-Type")
	
	if contentType == "application/msgpack" {
		// 这里应该解析msgpack格式，简化处理
		if err := c.ShouldBindJSON(&reports); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "解析在线用户数据失败",
				"error":   err.Error(),
			})
			return
		}
	} else {
		// JSON格式
		if err := c.ShouldBindJSON(&reports); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "解析在线用户数据失败",
				"error":   err.Error(),
			})
			return
		}
	}

	err := api.v2bxService.ReportAlive(token, reports)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "处理在线用户上报失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "在线用户上报处理成功",
	})
}

// GetAliveList 获取在线统计
func (api *V2bXAPI) GetAliveList(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		token = c.GetHeader("X-Node-Token")
		if token == "" {
			auth := c.GetHeader("Authorization")
			if len(auth) > 7 && auth[:7] == "Bearer " {
				token = auth[7:]
			}
		}
	}

	aliveList, err := api.v2bxService.GetAliveList(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "获取在线统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取在线统计成功",
		"data":    aliveList,
	})
}

// GetNodeStats 获取节点统计信息（管理接口）
func (api *V2bXAPI) GetNodeStats(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "节点ID格式错误",
		})
		return
	}

	stats, err := api.v2bxService.GetNodeStats(uint(nodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取节点统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取节点统计成功",
		"data":    stats,
	})
}

// CleanupExpiredOnlineUsers 清理过期在线用户（管理接口）
func (api *V2bXAPI) CleanupExpiredOnlineUsers(c *gin.Context) {
	err := api.v2bxService.CleanupExpiredOnlineUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "清理过期在线用户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "清理过期在线用户成功",
	})
}