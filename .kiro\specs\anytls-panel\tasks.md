# Implementation Plan

## 已完成任务

✅ **任务1** - 项目初始化和基础架构搭建
- 创建项目目录结构，包含前端、后端、数据库迁移等模块
- 配置Go模块依赖，包括Gin、GORM、JWT等核心库
- 设置前端React项目，配置TypeScript、Ant Design Pro等依赖
- 创建本地开发环境配置文件和数据库连接配置
- 设置开发环境的热重载和调试配置
- _Requirements: 10.1_

✅ **任务2** - 数据库设计和模型实现
- 创建基础数据库表结构，包含users、nodes、权限组、商品系统等表
- 实现Go数据模型和GORM配置，包含完整的关联关系
- 设置表索引和外键约束，确保数据完整性
- _Requirements: 1.3, 2.3, 4.1, 5.1, 8.1, 8.3_

✅ **任务3** - 后端API核心服务实现 (2024-01-15 完成)
- 3.1 实现身份认证和授权系统 - JWT中间件、用户状态验证、管理员权限控制
- 3.2 实现用户管理服务 - 完整的CRUD操作、UUID生成、流量统计
- 3.3 实现权限组管理服务 - 权限组CRUD、节点权限分配、用户权限检查
- 3.4 实现节点管理服务 - 节点CRUD、AnyTLS协议支持、健康检查
- _Requirements: 1.1-1.6, 2.1-2.6, 6.1, 7.3, 8.1-8.2_

✅ **任务4** - 商品系统核心服务实现 (2024-01-20 完成)
- 4.1 实现商品管理服务 - 商品CRUD、状态管理、权限组关联
- 4.2 实现订单和支付服务 - 订单生成、状态管理、支付处理
- 4.3 实现订阅管理服务 - 订阅创建、状态管理、过期检查、权限控制
- 完整的商品系统API端点，支持RESTful操作
- _Requirements: 8.3, 8.4, 8.5, 9.1, 9.2, 9.3, 9.4, 9.5, 9.6_

✅ **任务5** - V2bX兼容API接口实现 (2024-01-15 完成)
- 5.1 实现UniProxy配置接口 - 节点配置生成、权限验证、ETag缓存
- 5.2 实现用户列表和流量上报接口 - 用户管理、流量处理、在线用户监控
- 完整的V2bX UniProxy API支持，兼容现有V2bX节点
- _Requirements: 5.1-5.5_

✅ **任务6** - 支付系统集成 (2024-01-20 完成)
- 6.1 实现支付系统数据模型 - 支付方式、支付日志
- 6.2 实现支付网关接口 - 支付宝当面付、微信支付Native
- 6.3 实现支付回调处理逻辑 - 签名验证、订单状态更新
- 6.4 实现退款处理逻辑 - 基础退款框架
- 完整的支付API端点，支持多种支付方式
- _Requirements: 9.1, 9.2, 9.3_

## 待完成任务

- [ ] 7. 流量统计和监控系统
- [ ] 8. 订阅生成和配置管理
- [ ] 9. 基于Ant Design Pro的管理员界面开发
- [ ] 10. 基于Ant Design Pro的用户界面开发
- [ ] 11. 系统安全和性能优化
- [ ] 12. 配置管理和本地部署
- [ ] 13. 测试和质量保证
- [ ] 14. 文档和部署指南
- [ ] 15. Docker化部署方案（可选）

---

## 详细任务列表

- [x] 1. 项目初始化和基础架构搭建
  - 创建项目目录结构，包含前端、后端、数据库迁移等模块
  - 配置Go模块依赖，包括Gin、GORM、JWT等核心库
  - 设置前端React项目，配置TypeScript、Ant Design Pro等依赖
  - 创建本地开发环境配置文件和数据库连接配置
  - 设置开发环境的热重载和调试配置
  - _Requirements: 10.1_

- [x] 2. 数据库设计和模型实现
  - [x] 2.1 创建基础数据库表结构
    - 编写数据库迁移文件，创建users、nodes、user_nodes、traffic_logs、online_users表
    - 创建权限组和商品系统相关表：permission_groups、permission_group_nodes、products、orders、user_subscriptions
    - 设置表索引和外键约束，确保数据完整性
    - 创建初始化数据脚本，包含默认管理员账户和基础权限组
    - _Requirements: 1.3, 2.3, 4.1, 8.1, 8.3_

  - [x] 2.2 实现Go数据模型和GORM配置
    - 定义User、Node、TrafficLog、OnlineUser等基础结构体
    - 定义PermissionGroup、Product、Order、UserSubscription等商品系统结构体
    - 配置GORM关联关系，实现多对多用户节点关联和权限组关联
    - 实现数据模型验证和序列化方法
    - 编写数据库连接池和配置管理代码
    - _Requirements: 1.3, 2.3, 5.1, 8.1, 8.3_

- [x] 3. 后端API核心服务实现
  - [x] 3.1 实现身份认证和授权系统
    - 创建JWT令牌生成和验证中间件
    - 实现用户登录、注册、密码重置功能
    - 创建基于角色的访问控制(RBAC)中间件
    - 实现会话管理和令牌刷新机制
    - 修复JWT密钥不一致问题，完善用户状态验证
    - _Requirements: 6.1, 7.3_

  - [x] 3.2 实现用户管理服务
    - 创建UserService，实现用户CRUD操作
    - 实现用户列表查询、搜索、分页功能
    - 创建用户状态管理，包括激活、禁用、过期处理
    - 实现用户流量限制和设备限制逻辑
    - 添加UUID生成和时间解析功能
    - 实现用户流量统计和状态检查
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

  - [x] 3.3 实现权限组管理服务
    - 创建PermissionGroupService，实现权限组CRUD操作
    - 实现节点与权限组的关联管理
    - 创建权限组的节点分配和权限验证逻辑
    - 实现基于权限组的用户节点访问控制
    - 支持权限组的增删改查操作
    - 实现节点权限分配和用户权限检查
    - _Requirements: 8.1, 8.2_

  - [x] 3.4 实现节点管理服务
    - 创建NodeService，实现节点CRUD操作
    - 实现AnyTLS节点配置验证和存储
    - 创建节点状态监控和健康检查功能
    - 集成权限组系统，实现基于权限的节点访问控制
    - 添加AnyTLS协议支持和配置验证
    - 实现节点健康检查和统计功能
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 4. 商品系统核心服务实现
  - [x] 4.1 实现商品管理服务
    - 创建ProductService，实现商品CRUD操作
    - 实现商品与权限组的关联管理
    - 创建商品状态管理和排序功能
    - 实现商品价格和规格配置逻辑
    - _Requirements: 8.3, 8.4, 8.5_

  - [x] 4.2 实现订单和支付服务
    - 创建OrderService，实现订单生成和状态管理
    - 实现支付流程和订单确认逻辑
    - 创建用户订阅激活和权限分配功能
    - 实现订单历史查询和统计功能
    - _Requirements: 9.1, 9.2, 9.3_

  - [x] 4.3 实现订阅管理服务
    - 创建SubscriptionService，管理用户订阅状态
    - 实现订阅过期检查和自动处理
    - 创建基于权限组的节点访问控制
    - 实现订阅续费和升级逻辑
    - _Requirements: 8.7, 9.4, 9.5, 9.6_

- [x] 5. V2bX兼容API接口实现
  - [x] 5.1 实现UniProxy配置接口
    - 创建`/api/v1/server/UniProxy/config`端点
    - 实现AnyTLS节点配置格式转换
    - 集成权限组系统，只返回节点有权限的配置
    - 支持ETag缓存机制，优化性能
    - 实现节点认证和权限验证
    - 完善V2bXService的节点配置生成逻辑
    - _Requirements: 5.1_

  - [x] 5.2 实现用户列表和流量上报接口
    - 创建`/api/v1/server/UniProxy/user`端点，基于权限组返回节点用户列表
    - 实现`/api/v1/server/UniProxy/push`端点，接收流量数据
    - 创建`/api/v1/server/UniProxy/alive`端点，处理在线用户上报
    - 实现`/api/v1/server/UniProxy/alivelist`端点，返回在线统计
    - 支持msgpack和JSON两种数据格式
    - 完善流量数据处理和在线用户管理
    - _Requirements: 5.2, 5.3, 5.4, 5.5_

- [x] 6. 支付系统集成
  - [x] 6.1 实现支付系统数据模型
    - 创建Payment和PaymentLog数据模型
    - 实现支付方式配置和存储
    - 设计支付日志记录和审计功能
    - _Requirements: 9.1, 9.2, 9.3_

  - [x] 6.2 实现支付网关接口
    - 创建支付宝当面付网关 (AlipayF2FGateway)
    - 创建微信支付Native网关 (WechatPayNativeGateway)
    - 实现统一的支付网关接口设计
    - 支持多种支付方式的扩展架构
    - _Requirements: 9.1, 9.2, 9.3_

  - [x] 6.3 实现支付回调处理逻辑
    - 创建支付回调处理服务
    - 实现签名验证和安全检查
    - 自动更新订单状态和创建订阅
    - 支持多种回调格式处理
    - _Requirements: 9.1, 9.2, 9.3_

  - [x] 6.4 实现退款处理逻辑
    - 创建基础退款框架
    - 实现退款请求处理
    - 支持退款状态跟踪
    - _Requirements: 9.1, 9.2, 9.3_

- [ ] 6. 流量统计和监控系统
  - [ ] 6.1 实现流量数据处理服务
    - 创建TrafficService，处理V2bX上报的流量数据
    - 实现流量数据聚合和统计计算
    - 创建流量超限检测和用户状态更新逻辑
    - 集成订阅系统，检查用户订阅状态和流量限制
    - 实现流量数据清理和归档功能
    - _Requirements: 4.1, 4.2, 4.3, 4.6_

  - [ ] 6.2 实现在线用户监控
    - 创建OnlineUserService，管理用户在线状态
    - 实现实时在线用户统计和设备限制检查
    - 创建用户连接历史记录和分析功能
    - 集成权限组系统，验证用户节点访问权限
    - 实现异常连接检测和告警机制
    - _Requirements: 4.4, 4.5_

- [ ] 7. 订阅生成和配置管理
  - [ ] 7.1 实现基于权限组的订阅生成核心架构
    - 创建ClientConfigGenerator接口，支持多种客户端格式
    - 实现协议工厂模式，支持AnyTLS协议的多客户端配置生成
    - 更新SubscriptionService，集成权限组和订阅状态检查
    - 实现基于用户订阅和权限组的节点筛选逻辑
    - 实现订阅缓存和自动更新机制
    - _Requirements: 3.3, 3.4, 3.5, 3.6, 3.8, 8.7_

  - [ ] 7.2 实现Clash客户端配置生成
    - 创建ClashConfigGenerator，生成YAML格式的Clash配置
    - 实现AnyTLS协议的Clash代理配置转换
    - 添加代理组配置，包含节点选择和自动选择
    - 集成基础分流规则，支持国内外分流
    - 只包含用户权限组内的可用节点
    - _Requirements: 3.4_

  - [ ] 7.3 实现Sing-box客户端配置生成
    - 创建SingBoxConfigGenerator，生成JSON格式的Sing-box配置
    - 实现AnyTLS协议的Sing-box出站配置转换
    - 添加路由规则配置，支持GeoIP和GeoSite分流
    - 集成入站代理配置，支持混合代理模式
    - 只包含用户权限组内的可用节点
    - _Requirements: 3.5_

  - [ ] 7.4 实现V2Ray和通用客户端配置生成
    - 创建V2RayConfigGenerator，生成Base64编码的URI订阅
    - 实现AnyTLS URI格式生成，兼容V2Ray、Shadowrocket等客户端
    - 支持单节点URI生成和批量订阅生成
    - 实现通用格式配置，作为默认订阅格式
    - 只包含用户权限组内的可用节点
    - _Requirements: 3.6, 3.7_

  - [ ] 7.5 实现订阅API接口和访问控制
    - 创建多客户端订阅API端点，支持不同格式的订阅获取
    - 实现订阅token认证和用户权限验证
    - 集成订阅状态检查，过期用户返回空配置
    - 添加订阅访问日志和统计功能
    - 实现订阅链接的动态生成和管理
    - _Requirements: 3.11, 3.12, 9.6_

- [ ] 8. 基于Ant Design Pro的管理员界面开发
  - [ ] 8.1 搭建Ant Design Pro基础架构
    - 使用@ant-design/pro-cli创建项目脚手架
    - 配置ProLayout布局组件，包含侧边栏导航和顶部菜单
    - 集成React Router v6和权限路由控制
    - 配置主题定制和响应式布局
    - 集成React Query进行数据状态管理
    - _Requirements: 10.1_

  - [ ] 8.2 实现管理员仪表板
    - 使用ProCard和Statistic组件创建数据统计卡片
    - 集成Ant Design Charts实现流量趋势图表
    - 创建在线用户分布和节点状态监控图表
    - 添加商品销售统计和收入分析图表
    - 实现实时数据刷新和自动更新机制
    - 添加数据导出和报表生成功能
    - _Requirements: 4.2, 4.4, 4.5_

  - [ ] 8.3 实现权限组管理界面
    - 使用ProTable创建权限组列表，支持搜索、筛选、分页
    - 实现权限组创建和编辑表单，包含节点分配功能
    - 创建节点分配界面，支持拖拽和批量操作
    - 实现权限组与节点的关联可视化
    - 添加权限组使用统计和分析功能
    - _Requirements: 8.1, 8.2_

  - [ ] 8.4 实现商品管理界面
    - 使用ProTable创建商品列表，显示价格、流量、权限组等信息
    - 实现商品创建和编辑表单，包含完整的商品配置
    - 创建商品与权限组的关联管理
    - 实现商品状态管理和排序功能
    - 添加商品销售统计和分析功能
    - _Requirements: 8.3, 8.4, 8.5_

  - [ ] 8.5 实现用户管理界面
    - 使用ProTable组件创建用户列表，支持搜索、筛选、分页
    - 实现ProForm用户创建和编辑表单，包含完整的数据验证
    - 创建用户详情Modal，显示订阅状态、流量使用历史和设备连接信息
    - 实现批量操作功能，支持批量启用、禁用、删除用户
    - 添加用户订阅管理和手动续费功能
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

  - [ ] 8.6 实现节点管理界面
    - 使用ProTable创建节点列表，显示协议类型、权限组、节点状态和实时统计
    - 实现协议选择器，当前支持AnyTLS，预留其他协议扩展接口
    - 创建AnyTLS专用配置表单，包含填充策略等特定配置
    - 集成权限组选择，管理节点访问权限
    - 实现节点连接测试和诊断工具
    - 添加节点性能监控图表和告警功能
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

  - [ ] 8.7 实现订单管理界面
    - 创建订单列表页面，显示订单状态、用户信息、商品信息
    - 实现订单状态管理，支持手动确认和退款处理
    - 创建订单详情页面，显示完整的订单信息和支付记录
    - 实现订单搜索和筛选功能
    - 添加订单统计和收入分析功能
    - _Requirements: 9.1, 9.2, 9.3_

  - [ ] 8.8 实现流量分析和报表系统
    - 创建流量分析仪表板，使用多种图表展示数据趋势
    - 实现用户流量详情页面，支持时间范围筛选和对比
    - 创建自定义报表生成器，支持多种数据维度
    - 实现数据导出功能，支持CSV、Excel格式
    - 添加流量异常检测和告警功能
    - _Requirements: 4.2, 4.4, 4.5_

- [ ] 9. 基于Ant Design Pro的用户界面开发
  - [ ] 9.1 实现商品商店界面
    - 创建商品展示页面，使用卡片式布局展示可购买商品
    - 实现商品详情页面，显示价格、流量、有效期、可用节点等信息
    - 创建购买流程界面，包含商品选择、订单确认、支付处理
    - 实现购买成功页面和订阅激活提示
    - 添加商品推荐和优惠活动展示
    - _Requirements: 9.1, 9.2, 9.3_

  - [ ] 9.2 创建用户仪表板
    - 使用ProCard创建账户状态概览，包含订阅信息和流量使用进度条
    - 实现账户到期提醒和状态异常告警组件
    - 创建订阅信息卡片，显示当前套餐和剩余时间
    - 实现可用节点列表展示，基于权限组显示可访问节点
    - 添加续费提醒和快速购买入口
    - 优化移动端响应式布局和触摸交互
    - _Requirements: 7.1, 7.2_

  - [ ] 9.3 实现订阅中心
    - 创建订阅配置页面，支持多种客户端配置格式
    - 实现基于权限组的配置生成系统
    - 只显示用户有权限访问的节点配置
    - 创建客户端设置指南和使用教程页面
    - 实现订阅链接管理，支持重新生成和分享
    - 添加配置更新通知和版本管理功能
    - _Requirements: 3.1, 3.2, 3.3, 3.4_

  - [ ] 9.4 实现个人设置中心
    - 使用ProForm创建个人资料编辑表单
    - 实现安全的密码修改功能，包含强度验证
    - 创建订阅历史和流量统计页面
    - 实现账户安全设置和登录日志查看
    - 添加个性化设置和主题切换功能
    - _Requirements: 7.3, 7.4, 7.5, 7.6_

  - [ ] 9.5 实现用户帮助和支持系统
    - 创建AnyTLS客户端配置指南和常见问题解答
    - 实现在线帮助文档和视频教程集成
    - 创建问题反馈和工单提交功能
    - 实现系统公告和维护通知展示
    - 添加用户反馈收集和满意度调查
    - _Requirements: 7.4, 7.5_

- [ ] 10. 系统安全和性能优化
  - [ ] 10.1 实现安全防护机制
    - 创建API速率限制中间件，防止恶意请求
    - 实现IP封禁和异常访问检测
    - 创建操作日志记录和审计功能
    - 实现数据加密和敏感信息保护
    - 添加支付安全和订单防重复提交机制
    - _Requirements: 6.2, 6.3_

  - [ ] 10.2 实现系统监控和日志
    - 创建系统健康检查和监控端点
    - 实现结构化日志记录和日志轮转
    - 创建性能指标收集和分析
    - 实现错误追踪和告警机制
    - 添加商品系统和支付相关的监控指标
    - _Requirements: 6.3, 6.4_

- [ ] 11. 配置管理和本地部署
  - [ ] 11.1 实现配置管理和环境变量
    - 创建配置文件模板和环境变量映射
    - 实现配置热重载和动态更新
    - 创建数据库迁移和初始化脚本，包含商品系统表结构
    - 实现本地开发环境的配置管理
    - 添加支付配置和商品系统配置管理
    - _Requirements: 6.4, 6.5_

  - [ ] 11.2 创建本地部署和运行脚本
    - 编写本地环境启动脚本，包含前后端服务启动
    - 创建数据库初始化和种子数据脚本
    - 实现开发环境的服务管理和监控
    - 创建本地环境的备份和恢复功能
    - 添加开发调试工具和日志配置
    - _Requirements: 6.6_

- [ ] 12. 测试和质量保证
  - [ ] 12.1 编写单元测试和集成测试
    - 为所有Service层业务逻辑编写单元测试，包含商品系统相关服务
    - 创建API接口集成测试，确保功能正确性
    - 实现数据库操作测试和事务处理验证
    - 编写前端组件测试和用户交互测试
    - 添加商品购买流程和支付相关的测试
    - _Requirements: 所有功能需求_

  - [ ] 12.2 实现端到端测试和性能测试
    - 创建用户注册登录流程的端到端测试
    - 实现V2bX API兼容性测试
    - 创建商品购买和订阅激活的端到端测试
    - 创建负载测试，验证系统性能指标
    - 实现安全测试，检查潜在漏洞
    - 添加支付安全和订单处理的安全测试
    - _Requirements: 所有功能需求_

- [ ] 13. 文档和部署指南
  - [ ] 12.1 实现权限组管理系统
    - 创建PermissionGroup数据模型和数据库表
    - 实现权限组CRUD操作的后端API
    - 创建权限组管理界面，支持节点分配和权限设置
    - 实现节点与权限组的多对多关联管理
    - 添加权限组的排序和状态管理功能
    - _Requirements: 8.1, 8.2_

  - [ ] 12.2 实现订阅商品管理系统
    - 创建Product、Order、UserSubscription数据模型
    - 实现商品CRUD操作和商品状态管理
    - 创建商品管理界面，包含价格、流量、有效期等配置
    - 实现商品与权限组的关联，控制节点访问权限
    - 添加商品排序、分类和批量操作功能
    - _Requirements: 8.3, 8.4, 8.5_

  - [ ] 12.3 实现用户购买和订单系统
    - 创建订单生成和状态管理逻辑
    - 实现用户商品购买流程和订单确认
    - 创建用户商品商店界面，展示可购买商品
    - 实现购买确认、支付集成和订阅激活流程
    - 添加订单历史查询和状态跟踪功能
    - _Requirements: 9.1, 9.2, 9.3_

  - [ ] 12.4 实现基于权限组的订阅生成
    - 更新订阅生成逻辑，基于用户权限组筛选可用节点
    - 实现用户订阅状态检查和权限验证
    - 更新多客户端配置生成，只包含用户有权限的节点
    - 实现订阅过期处理和权限回收机制
    - 添加订阅续费和升级功能
    - _Requirements: 8.7, 9.4, 9.5, 9.6_

  - [ ] 12.5 实现商品系统管理界面
    - 创建管理员商品管理页面，支持商品的完整生命周期管理
    - 实现权限组管理界面，支持节点分配和权限可视化
    - 创建订单管理界面，支持订单状态更新和退款处理
    - 实现收入统计和商品销售分析功能
    - 添加商品推荐和营销活动管理功能
    - _Requirements: 8.3, 8.4, 8.5_

- [ ] 13. 文档和部署指南
  - [ ] 13.1 编写用户和管理员文档
    - 创建系统安装和配置指南
    - 编写管理员操作手册和最佳实践
    - 创建用户使用教程和客户端配置指南
    - 编写API文档和开发者指南
    - 添加商品系统和权限组管理文档
    - 创建商品购买和订阅管理用户指南
    - _Requirements: 7.4, 7.5_

  - [ ] 13.2 创建部署和运维文档
    - 编写本地部署指南和环境配置说明
    - 创建数据库维护和备份恢复指南
    - 编写监控和故障排除文档
    - 创建升级和迁移指南
    - 添加商品系统的运维和维护指南
    - 创建支付系统配置和故障排除指南
    - _Requirements: 6.6_

- [ ] 14. Docker化部署方案（可选）
  - [ ] 14.1 创建Docker化部署配置
    - 编写Dockerfile，优化镜像大小和构建速度
    - 创建docker-compose配置，包含所有服务依赖
    - 实现多阶段构建，分离开发和生产环境
    - 创建健康检查和自动重启机制
    - 添加商品系统相关的环境变量配置
    - _Requirements: 6.6_

  - [ ] 14.2 Docker部署文档和脚本
    - 编写Docker部署指南和最佳实践
    - 创建Docker环境的配置管理脚本
    - 实现容器化环境的监控和日志收集
    - 创建Docker环境的备份和恢复方案
    - 添加容器编排和扩展配置
    - _Requirements: 6.6_