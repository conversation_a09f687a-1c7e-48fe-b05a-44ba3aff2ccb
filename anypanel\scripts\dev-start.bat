@echo off
REM AnyPanel 开发环境启动脚本 (Windows)

echo 🚀 启动 AnyPanel 开发环境...

REM 检查 Docker 是否运行
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未运行，请先启动 Docker
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "frontend\dist" mkdir frontend\dist

REM 复制开发环境配置文件
if not exist "backend\config.dev.yaml" (
    copy "backend\config.yaml" "backend\config.dev.yaml"
    echo 📝 已创建开发环境配置文件 backend\config.dev.yaml
)

REM 启动 Docker Compose 服务
echo 🐳 启动 Docker Compose 服务...
docker-compose up -d mysql redis

REM 等待数据库启动
echo ⏳ 等待数据库启动...
timeout /t 10 /nobreak >nul

REM 初始化数据库
echo 📊 初始化数据库...
docker-compose exec mysql mysql -uroot -prootpassword anypanel < scripts\init.sql

REM 启动后端服务
echo 🔧 启动后端服务...
cd backend
go mod tidy
start "Backend Server" go run cmd/main.go
cd ..

REM 等待后端服务启动
echo ⏳ 等待后端服务启动...
timeout /t 5 /nobreak >nul

REM 启动前端服务
echo 🎨 启动前端服务...
cd frontend
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
)
start "Frontend Server" npm run dev
cd ..

echo ✅ 开发环境启动完成！
echo.
echo 📊 管理面板: http://localhost:3000
echo 🔗 API 服务: http://localhost:8080
echo 🗄️  数据库: localhost:3306
echo 📈 Redis: localhost:6379
echo.
echo 默认管理员账户:
echo 用户名: admin
echo 密码: password
echo.
echo 按任意键停止所有服务...
pause >nul

REM 停止服务
echo 🛑 正在停止服务...
taskkill /FI "WINDOWTITLE eq Backend Server*" /F >nul 2>&1
taskkill /FI "WINDOWTITLE eq Frontend Server*" /F >nul 2>&1
docker-compose down
echo ✅ 所有服务已停止