#!/bin/bash

# AnyPanel 开发环境启动脚本

echo "🚀 启动 AnyPanel 开发环境..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 创建必要的目录
mkdir -p logs
mkdir -p frontend/dist

# 复制开发环境配置文件
if [ ! -f backend/config.dev.yaml ]; then
    cp backend/config.yaml backend/config.dev.yaml
    echo "📝 已创建开发环境配置文件 backend/config.dev.yaml"
fi

# 启动 Docker Compose 服务
echo "🐳 启动 Docker Compose 服务..."
docker-compose up -d mysql redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 初始化数据库
echo "📊 初始化数据库..."
docker-compose exec mysql mysql -uroot -prootpassword anypanel < scripts/init.sql

# 启动后端服务
echo "🔧 启动后端服务..."
cd backend
go mod tidy
go run cmd/main.go &
BACKEND_PID=$!
cd ..

# 等待后端服务启动
echo "⏳ 等待后端服务启动..."
sleep 5

# 启动前端服务
echo "🎨 启动前端服务..."
cd frontend
npm install
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ 开发环境启动完成！"
echo ""
echo "📊 管理面板: http://localhost:3000"
echo "🔗 API 服务: http://localhost:8080"
echo "🗄️  数据库: localhost:3306"
echo "📈 Redis: localhost:6379"
echo ""
echo "默认管理员账户:"
echo "用户名: admin"
echo "密码: password"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID; docker-compose down; exit 0' INT
wait