#!/bin/bash

echo "🚀 启动本地开发环境..."

# 检查依赖
check_dependencies() {
    echo "🔧 检查依赖..."
    
    # 检查Go
    if ! command -v go &> /dev/null; then
        echo "❌ Go未安装或未添加到PATH"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js未安装或未添加到PATH"
        exit 1
    fi
    
    # 检查MySQL
    if ! command -v mysql &> /dev/null; then
        echo "❌ MySQL未安装或未添加到PATH"
        exit 1
    fi
    
    # 检查Redis
    if ! command -v redis-cli &> /dev/null; then
        echo "❌ Redis未安装或未添加到PATH"
        exit 1
    fi
    
    echo "✅ 依赖检查完成"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装依赖..."
    
    # 安装后端依赖
    echo "📦 安装后端依赖..."
    cd backend
    go mod tidy
    cd ..
    
    # 安装前端依赖
    echo "📦 安装前端依赖..."
    cd frontend
    npm install
    cd ..
    
    echo "✅ 依赖安装完成"
}

# 检查服务状态
check_services() {
    echo "🗄️ 检查服务状态..."
    
    # 检查MySQL
    if ! mysqladmin ping -h localhost --silent; then
        echo "❌ MySQL服务未运行，请启动MySQL服务"
        exit 1
    fi
    
    # 检查Redis
    if ! redis-cli ping > /dev/null 2>&1; then
        echo "❌ Redis服务未运行，请启动Redis服务"
        exit 1
    fi
    
    echo "✅ 服务检查完成"
}

# 检查配置文件
check_config() {
    echo "📝 检查配置文件..."
    
    if [ ! -f "backend/config.yaml" ]; then
        echo "❌ 配置文件不存在: backend/config.yaml"
        exit 1
    fi
    
    echo "✅ 配置文件检查完成"
}

# 启动服务
start_services() {
    echo "🔧 启动服务..."
    
    # 启动后端服务
    echo "🔧 启动后端服务..."
    cd backend
    go run cmd/main.go &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端服务启动
    echo "⏳ 等待后端服务启动..."
    sleep 5
    
    # 启动前端服务
    echo "🎨 启动前端服务..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    echo "✅ 服务启动完成"
    echo ""
    echo "🌐 访问地址："
    echo "   管理面板: http://localhost:3000"
    echo "   API服务: http://localhost:8080"
    echo ""
    echo "🔑 默认账户："
    echo "   用户名: admin"
    echo "   密码: password"
    echo ""
    echo "💡 按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    wait $BACKEND_PID $FRONTEND_PID
}

# 清理函数
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    
    # 停止后端服务
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    
    # 停止前端服务
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
    fi
    
    # 停止所有相关的node进程
    pkill -f "npm run dev" 2>/dev/null
    pkill -f "vite" 2>/dev/null
    
    echo "✅ 服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    check_dependencies
    install_dependencies
    check_services
    check_config
    start_services
}

# 运行主函数
main