package main

import (
	"anypanel/internal/api"
	"anypanel/internal/config"
	"anypanel/internal/database"
	"anypanel/internal/model"
	"anypanel/internal/service"
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置日志级别
	if cfg.Debug {
		logrus.SetLevel(logrus.DebugLevel)
		gin.SetMode(gin.DebugMode)
	} else {
		logrus.SetLevel(logrus.InfoLevel)
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化数据库
	db, rdb, err := database.InitDatabase(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	
	// 检查是否需要初始化种子数据
	seeded, err := database.IsDataSeeded(db)
	if err != nil {
		log.Fatalf("Failed to check data seed status: %v", err)
	}
	
	if !seeded {
		// 初始化种子数据
		if err := database.SeedData(db); err != nil {
			log.Fatalf("Failed to seed data: %v", err)
		}
	}

	// 创建路由
	router := api.NewRouter(cfg, db, rdb)

	// 启动定时任务调度器
	scheduler := service.NewTaskScheduler(db)
	if err := scheduler.Start(); err != nil {
		logrus.Fatalf("Failed to start task scheduler: %v", err)
	}
	logrus.Info("Task scheduler started successfully")

	// 设置优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动服务器
	server := &http.Server{
		Addr:    cfg.Server.Address,
		Handler: router,
	}

	// 在goroutine中启动服务器
	go func() {
		logrus.Infof("Starting AnyPanel server on %s", cfg.Server.Address)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logrus.Info("Shutting down server...")

	// 停止定时任务调度器
	scheduler.Stop()
	logrus.Info("Task scheduler stopped")

	// 优雅关闭HTTP服务器
	ctx, cancel = context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		logrus.Fatal("Server forced to shutdown:", err)
	}

	logrus.Info("Server exited")
}