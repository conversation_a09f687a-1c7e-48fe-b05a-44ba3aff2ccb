package main

import (
	"anypanel/internal/api"
	"anypanel/internal/config"
	"anypanel/internal/database"
	"anypanel/internal/model"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置日志级别
	if cfg.Debug {
		logrus.SetLevel(logrus.DebugLevel)
		gin.SetMode(gin.DebugMode)
	} else {
		logrus.SetLevel(logrus.InfoLevel)
		gin.SetMode(gin.ReleaseMode)
	}

	// 初始化数据库
	db, rdb, err := database.InitDatabase(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	
	// 检查是否需要初始化种子数据
	seeded, err := database.IsDataSeeded(db)
	if err != nil {
		log.Fatalf("Failed to check data seed status: %v", err)
	}
	
	if !seeded {
		// 初始化种子数据
		if err := database.SeedData(db); err != nil {
			log.Fatalf("Failed to seed data: %v", err)
		}
	}

	// 创建路由
	router := api.NewRouter(cfg, db, rdb)

	// 启动服务器
	logrus.Infof("Starting AnyPanel server on %s", cfg.Server.Address)
	if err := router.Run(cfg.Server.Address); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}