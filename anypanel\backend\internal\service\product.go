package service

import (
	"anypanel/internal/model"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type ProductService struct {
	db *gorm.DB
}

func NewProductService(db *gorm.DB) *ProductService {
	return &ProductService{
		db: db,
	}
}

// ProductQueryRequest 商品查询请求
type ProductQueryRequest struct {
	Page       int    `form:"page" binding:"min=1"`
	PageSize   int    `form:"page_size" binding:"min=1,max=100"`
	Search     string `form:"search"`
	Status     string `form:"status"`
	SortBy     string `form:"sort_by"`
	SortOrder  string `form:"sort_order"`
}

// ProductQueryResponse 商品查询响应
type ProductQueryResponse struct {
	Total    int64        `json:"total"`
	Products []*ProductDTO `json:"products"`
}

// ProductCreateRequest 商品创建请求
type ProductCreateRequest struct {
	Name            string  `json:"name" binding:"required,min=1,max=100"`
	Description     string  `json:"description"`
	Price           float64 `json:"price" binding:"required,min=0"`
	TrafficLimit    int64   `json:"traffic_limit" binding:"required,min=0"`
	DurationDays    int     `json:"duration_days" binding:"required,min=1"`
	DeviceLimit     int     `json:"device_limit" binding:"required,min=1"`
	SpeedLimit      int     `json:"speed_limit" binding:"required,min=0"`
	PermissionGroupID uint   `json:"permission_group_id" binding:"required"`
	Status          string  `json:"status" binding:"oneof=active inactive"`
	SortOrder       int     `json:"sort_order" binding:"min=0"`
}

// ProductUpdateRequest 商品更新请求
type ProductUpdateRequest struct {
	Name            *string  `json:"name"`
	Description     *string  `json:"description"`
	Price           *float64 `json:"price"`
	TrafficLimit    *int64   `json:"traffic_limit"`
	DurationDays    *int     `json:"duration_days"`
	DeviceLimit     *int     `json:"device_limit"`
	SpeedLimit      *int     `json:"speed_limit"`
	PermissionGroupID *uint   `json:"permission_group_id"`
	Status          *string  `json:"status"`
	SortOrder       *int     `json:"sort_order"`
}

// ProductDTO 商品数据传输对象
type ProductDTO struct {
	ID              uint              `json:"id"`
	Name            string            `json:"name"`
	Description     string            `json:"description"`
	Price           float64           `json:"price"`
	TrafficLimit    int64             `json:"traffic_limit"`
	DurationDays    int               `json:"duration_days"`
	DeviceLimit     int               `json:"device_limit"`
	SpeedLimit      int               `json:"speed_limit"`
	PermissionGroupID uint             `json:"permission_group_id"`
	PermissionGroup PermissionGroupDTO `json:"permission_group"`
	Status          string            `json:"status"`
	SortOrder       int               `json:"sort_order"`
	CreatedAt       string            `json:"created_at"`
	UpdatedAt       string            `json:"updated_at"`
}

// PermissionGroupDTO 权限组数据传输对象
type PermissionGroupDTO struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	SortOrder   int    `json:"sort_order"`
}

// GetProductByID 根据ID获取商品
func (s *ProductService) GetProductByID(productID uint) (*model.Product, error) {
	var product model.Product
	if err := s.db.Preload("PermissionGroup").First(&product, productID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("商品不存在")
		}
		return nil, err
	}
	return &product, nil
}

// QueryProducts 查询商品列表
func (s *ProductService) QueryProducts(req *ProductQueryRequest) (*ProductQueryResponse, error) {
	query := s.db.Model(&model.Product{})

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchPattern, searchPattern)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "sort_order"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询商品
	var products []model.Product
	if err := query.Preload("PermissionGroup").Find(&products).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	productDTOs := make([]*ProductDTO, len(products))
	for i, product := range products {
		productDTOs[i] = s.toProductDTO(&product)
	}

	return &ProductQueryResponse{
		Total:    total,
		Products: productDTOs,
	}, nil
}

// CreateProduct 创建商品
func (s *ProductService) CreateProduct(req *ProductCreateRequest) (*ProductDTO, error) {
	// 验证权限组是否存在
	var permissionGroup model.PermissionGroup
	if err := s.db.First(&permissionGroup, req.PermissionGroupID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("权限组不存在")
		}
		return nil, err
	}

	// 创建商品
	product := &model.Product{
		Name:            req.Name,
		Description:     req.Description,
		Price:           req.Price,
		TrafficLimit:    req.TrafficLimit,
		DurationDays:    req.DurationDays,
		DeviceLimit:     req.DeviceLimit,
		SpeedLimit:      req.SpeedLimit,
		PermissionGroupID: req.PermissionGroupID,
		Status:          req.Status,
		SortOrder:       req.SortOrder,
	}

	if err := product.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(product).Error; err != nil {
		return nil, err
	}

	// 重新加载以包含关联数据
	if err := s.db.Preload("PermissionGroup").First(product, product.ID).Error; err != nil {
		return nil, err
	}

	return s.toProductDTO(product), nil
}

// UpdateProduct 更新商品
func (s *ProductService) UpdateProduct(productID uint, req *ProductUpdateRequest) (*ProductDTO, error) {
	product, err := s.GetProductByID(productID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Name != nil {
		product.Name = *req.Name
	}

	if req.Description != nil {
		product.Description = *req.Description
	}

	if req.Price != nil {
		product.Price = *req.Price
	}

	if req.TrafficLimit != nil {
		product.TrafficLimit = *req.TrafficLimit
	}

	if req.DurationDays != nil {
		product.DurationDays = *req.DurationDays
	}

	if req.DeviceLimit != nil {
		product.DeviceLimit = *req.DeviceLimit
	}

	if req.SpeedLimit != nil {
		product.SpeedLimit = *req.SpeedLimit
	}

	if req.PermissionGroupID != nil {
		// 验证权限组是否存在
		var permissionGroup model.PermissionGroup
		if err := s.db.First(&permissionGroup, *req.PermissionGroupID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("权限组不存在")
			}
			return nil, err
		}
		product.PermissionGroupID = *req.PermissionGroupID
	}

	if req.Status != nil {
		product.Status = *req.Status
	}

	if req.SortOrder != nil {
		product.SortOrder = *req.SortOrder
	}

	if err := product.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(product).Error; err != nil {
		return nil, err
	}

	// 重新加载以包含关联数据
	if err := s.db.Preload("PermissionGroup").First(product, product.ID).Error; err != nil {
		return nil, err
	}

	return s.toProductDTO(product), nil
}

// DeleteProduct 删除商品
func (s *ProductService) DeleteProduct(productID uint) error {
	product, err := s.GetProductByID(productID)
	if err != nil {
		return err
	}

	// 检查是否有关联的订单或订阅
	var orderCount int64
	if err := s.db.Model(&model.Order{}).Where("product_id = ?", productID).Count(&orderCount).Error; err != nil {
		return err
	}

	var subscriptionCount int64
	if err := s.db.Model(&model.UserSubscription{}).Where("product_id = ?", productID).Count(&subscriptionCount).Error; err != nil {
		return err
	}

	if orderCount > 0 || subscriptionCount > 0 {
		return errors.New("该商品有关联的订单或订阅，无法删除")
	}

	if err := s.db.Delete(product).Error; err != nil {
		return err
	}

	return nil
}

// UpdateProductStatus 更新商品状态
func (s *ProductService) UpdateProductStatus(productID uint, status string) error {
	product, err := s.GetProductByID(productID)
	if err != nil {
		return err
	}

	product.Status = status
	if err := product.Validate(); err != nil {
		return err
	}

	return s.db.Save(product).Error
}

// GetProductsByPermissionGroup 根据权限组获取商品
func (s *ProductService) GetProductsByPermissionGroup(permissionGroupID uint) ([]*model.Product, error) {
	var products []model.Product
	if err := s.db.Where("permission_group_id = ? AND status = ?", permissionGroupID, "active").
		Order("sort_order ASC").
		Find(&products).Error; err != nil {
		return nil, err
	}

	productPtrs := make([]*model.Product, len(products))
	for i := range products {
		productPtrs[i] = &products[i]
	}

	return productPtrs, nil
}

// GetActiveProducts 获取活跃商品列表
func (s *ProductService) GetActiveProducts() ([]*model.Product, error) {
	var products []model.Product
	if err := s.db.Where("status = ?", "active").
		Order("sort_order ASC").
		Find(&products).Error; err != nil {
		return nil, err
	}

	activeProducts := make([]*model.Product, len(products))
	for i := range products {
		activeProducts[i] = &products[i]
	}

	return activeProducts, nil
}

// GetProductStats 获取商品统计信息
func (s *ProductService) GetProductStats() (map[string]interface{}, error) {
	var totalProducts int64
	var activeProducts int64
	var totalOrders int64
	var totalRevenue float64

	// 获取商品总数
	if err := s.db.Model(&model.Product{}).Count(&totalProducts).Error; err != nil {
		return nil, err
	}

	// 获取活跃商品数
	if err := s.db.Model(&model.Product{}).Where("status = ?", "active").Count(&activeProducts).Error; err != nil {
		return nil, err
	}

	// 获取订单总数
	if err := s.db.Model(&model.Order{}).Where("status = ?", "paid").Count(&totalOrders).Error; err != nil {
		return nil, err
	}

	// 获取总收入
	if err := s.db.Model(&model.Order{}).Where("status = ?", "paid").Select("COALESCE(SUM(amount), 0)").Scan(&totalRevenue).Error; err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"total_products":  totalProducts,
		"active_products": activeProducts,
		"total_orders":    totalOrders,
		"total_revenue":   totalRevenue,
	}

	return stats, nil
}

// 辅助函数
func (s *ProductService) toProductDTO(product *model.Product) *ProductDTO {
	return &ProductDTO{
		ID:              product.ID,
		Name:            product.Name,
		Description:     product.Description,
		Price:           product.Price,
		TrafficLimit:    product.TrafficLimit,
		DurationDays:    product.DurationDays,
		DeviceLimit:     product.DeviceLimit,
		SpeedLimit:      product.SpeedLimit,
		PermissionGroupID: product.PermissionGroupID,
		PermissionGroup: PermissionGroupDTO{
			ID:          product.PermissionGroup.ID,
			Name:        product.PermissionGroup.Name,
			Description: product.PermissionGroup.Description,
			SortOrder:   product.PermissionGroup.SortOrder,
		},
		Status:    product.Status,
		SortOrder: product.SortOrder,
		CreatedAt: product.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: product.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}