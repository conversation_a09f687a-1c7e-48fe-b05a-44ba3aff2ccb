package main

import (
	"anypanel/internal/api"
	"anypanel/internal/config"
	"anypanel/internal/database"
	"anypanel/internal/migrate"
	"anypanel/internal/model"
	"log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	db, err := database.Init(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 初始化Redis
	redisClient, err := database.InitRedis(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize Redis: %v", err)
	}

	// 执行数据库迁移
	migrator := migrate.NewMigrator(db)
	if err := migrator.Run(); err != nil {
		log.Fatalf("Failed to run migrations: %v", err)
	}

	// 自动迁移模型
	if err := db.AutoMigrate(
		&model.User{},
		&model.Node{},
		&model.PermissionGroup{},
		&model.Product{},
		&model.Order{},
		&model.UserSubscription{},
		&model.TrafficLog{},
		&model.OnlineUser{},
	); err != nil {
		log.Fatalf("Failed to auto migrate models: %v", err)
	}

	// 设置Gin模式
	if cfg.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 设置路由
	r := api.SetupRoutes(db, cfg)

	// 启动服务器
	address := cfg.Server.Address
	if address == "" {
		address = ":8080"
	}

	log.Printf("Starting server on %s", address)
	log.Printf("Debug mode: %v", cfg.Debug)
	log.Printf("Database: %s:%d/%s", cfg.DB.Host, cfg.DB.Port, cfg.DB.Database)
	log.Printf("Redis: %s:%d", cfg.Redis.Host, cfg.Redis.Port)

	if err := r.Run(address); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}