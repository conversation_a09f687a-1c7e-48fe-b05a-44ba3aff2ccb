-- 数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS anypanel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE anypanel;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
    traffic_limit BIGINT DEFAULT 0,
    traffic_used BIGINT DEFAULT 0,
    device_limit INT DEFAULT 1,
    speed_limit INT DEFAULT 0,
    expired_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建节点表
CREATE TABLE IF NOT EXISTS nodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    protocol VARCHAR(20) NOT NULL DEFAULT 'anytls',
    host VARCHAR(255) NOT NULL,
    port INT NOT NULL,
    password VARCHAR(255) NOT NULL,
    config JSON,
    server_name VARCHAR(255),
    status ENUM('online', 'offline', 'maintenance') DEFAULT 'offline',
    sort_order INT DEFAULT 0,
    traffic_rate DECIMAL(10,2) DEFAULT 1.0,
    max_users INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_protocol (protocol)
);

-- 创建用户节点关联表
CREATE TABLE IF NOT EXISTS user_nodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    node_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_node (user_id, node_id)
);

-- 创建流量统计表
CREATE TABLE IF NOT EXISTS traffic_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    node_id INT NOT NULL,
    upload BIGINT DEFAULT 0,
    download BIGINT DEFAULT 0,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE,
    INDEX idx_user_time (user_id, recorded_at),
    INDEX idx_node_time (node_id, recorded_at)
);

-- 创建在线用户表
CREATE TABLE IF NOT EXISTS online_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    node_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_node_ip (user_id, node_id, ip_address)
);

-- 创建权限组表
CREATE TABLE IF NOT EXISTS permission_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建权限组节点关联表
CREATE TABLE IF NOT EXISTS permission_group_nodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id INT NOT NULL,
    node_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES permission_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_group_node (group_id, node_id)
);

-- 创建订阅商品表
CREATE TABLE IF NOT EXISTS products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    traffic_limit BIGINT NOT NULL COMMENT '流量限制(字节)',
    duration_days INT NOT NULL COMMENT '有效期(天)',
    device_limit INT DEFAULT 1 COMMENT '设备数限制',
    speed_limit INT DEFAULT 0 COMMENT '速度限制(Mbps)',
    permission_group_id INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (permission_group_id) REFERENCES permission_groups(id)
);

-- 创建用户订阅表
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    order_id VARCHAR(50) UNIQUE,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expired_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_user_status (user_id, status),
    INDEX idx_expired_at (expired_at)
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'paid', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_id VARCHAR(100),
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_user_status (user_id, status),
    INDEX idx_order_id (order_id)
);

-- 创建默认管理员用户
INSERT INTO users (uuid, username, email, password_hash, role) 
VALUES ('admin-uuid-12345', 'admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- 创建默认权限组
INSERT INTO permission_groups (name, description, sort_order) VALUES 
('默认组', '默认权限组', 1),
('高级组', '高级用户权限组', 2),
('VIP组', 'VIP用户权限组', 3);

-- 创建示例商品
INSERT INTO products (name, description, price, traffic_limit, duration_days, device_limit, permission_group_id, status) VALUES
('基础套餐', '100GB流量，30天有效期', 9.99, 107374182400, 30, 1, 1, 'active'),
('高级套餐', '500GB流量，90天有效期', 24.99, ************, 90, 3, 2, 'active'),
('VIP套餐', '无限流量，365天有效期', 99.99, 0, 365, 5, 3, 'active');

-- 创建示例节点
INSERT INTO nodes (name, protocol, host, port, password, server_name, status, sort_order) VALUES
('香港节点01', 'anytls', 'hk.example.com', 8443, 'test-password', 'hk.example.com', 'online', 1),
('日本节点01', 'anytls', 'jp.example.com', 8443, 'test-password', 'jp.example.com', 'online', 2),
('美国节点01', 'anytls', 'us.example.com', 8443, 'test-password', 'us.example.com', 'online', 3);

-- 将节点分配到权限组
INSERT INTO permission_group_nodes (group_id, node_id) VALUES
(1, 1), (1, 2),
(2, 1), (2, 2), (2, 3),
(3, 1), (3, 2), (3, 3);