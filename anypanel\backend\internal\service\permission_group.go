package service

import (
	"anypanel/internal/model"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

type PermissionGroupService struct {
	db *gorm.DB
}

func NewPermissionGroupService(db *gorm.DB) *PermissionGroupService {
	return &PermissionGroupService{
		db: db,
	}
}

// PermissionGroupQueryRequest 权限组查询请求
type PermissionGroupQueryRequest struct {
	Page      int    `form:"page" binding:"min=1"`
	PageSize  int    `form:"page_size" binding:"min=1,max=100"`
	Search    string `form:"search"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order"`
}

// PermissionGroupQueryResponse 权限组查询响应
type PermissionGroupQueryResponse struct {
	Total            int64                   `json:"total"`
	PermissionGroups []*PermissionGroupDTO   `json:"permission_groups"`
}

// PermissionGroupCreateRequest 权限组创建请求
type PermissionGroupCreateRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Description string `json:"description"`
	SortOrder   int    `json:"sort_order"`
}

// PermissionGroupUpdateRequest 权限组更新请求
type PermissionGroupUpdateRequest struct {
	Name        *string `json:"name" binding:"omitempty,min=1,max=100"`
	Description *string `json:"description"`
	SortOrder   *int    `json:"sort_order"`
}

// PermissionGroupDTO 权限组数据传输对象
type PermissionGroupDTO struct {
	ID          uint             `json:"id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	SortOrder   int              `json:"sort_order"`
	CreatedAt   string           `json:"created_at"`
	UpdatedAt   string           `json:"updated_at"`
	NodeCount   int              `json:"node_count"`
	Nodes       []*NodeDTO        `json:"nodes,omitempty"`
	Products    []*ProductDTO     `json:"products,omitempty"`
}

// GetPermissionGroupByID 根据ID获取权限组
func (s *PermissionGroupService) GetPermissionGroupByID(groupID uint) (*model.PermissionGroup, error) {
	var group model.PermissionGroup
	if err := s.db.First(&group, groupID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("权限组不存在")
		}
		return nil, err
	}
	return &group, nil
}

// GetPermissionGroupByName 根据名称获取权限组
func (s *PermissionGroupService) GetPermissionGroupByName(name string) (*model.PermissionGroup, error) {
	var group model.PermissionGroup
	if err := s.db.Where("name = ?", name).First(&group).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("权限组不存在")
		}
		return nil, err
	}
	return &group, nil
}

// QueryPermissionGroups 查询权限组列表
func (s *PermissionGroupService) QueryPermissionGroups(req *PermissionGroupQueryRequest) (*PermissionGroupQueryResponse, error) {
	query := s.db.Model(&model.PermissionGroup{})

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchPattern, searchPattern)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "sort_order"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询权限组
	var groups []model.PermissionGroup
	if err := query.Find(&groups).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	groupDTOs := make([]*PermissionGroupDTO, len(groups))
	for i, group := range groups {
		groupDTOs[i] = s.toPermissionGroupDTO(&group, false)
	}

	return &PermissionGroupQueryResponse{
		Total:            total,
		PermissionGroups: groupDTOs,
	}, nil
}

// GetAllPermissionGroups 获取所有权限组
func (s *PermissionGroupService) GetAllPermissionGroups() ([]*model.PermissionGroup, error) {
	var groups []model.PermissionGroup
	if err := s.db.Order("sort_order ASC, id ASC").Find(&groups).Error; err != nil {
		return nil, err
	}

	result := make([]*model.PermissionGroup, len(groups))
	for i := range groups {
		result[i] = &groups[i]
	}

	return result, nil
}

// CreatePermissionGroup 创建权限组
func (s *PermissionGroupService) CreatePermissionGroup(req *PermissionGroupCreateRequest) (*PermissionGroupDTO, error) {
	// 检查权限组名称是否已存在
	if _, err := s.GetPermissionGroupByName(req.Name); err == nil {
		return nil, errors.New("权限组名称已存在")
	}

	group := &model.PermissionGroup{
		Name:        req.Name,
		Description: req.Description,
		SortOrder:   req.SortOrder,
	}

	if err := group.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(group).Error; err != nil {
		return nil, err
	}

	return s.toPermissionGroupDTO(group, false), nil
}

// UpdatePermissionGroup 更新权限组
func (s *PermissionGroupService) UpdatePermissionGroup(groupID uint, req *PermissionGroupUpdateRequest) (*PermissionGroupDTO, error) {
	group, err := s.GetPermissionGroupByID(groupID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Name != nil {
		// 检查权限组名称是否已被其他权限组使用
		var existingGroup model.PermissionGroup
		if err := s.db.Where("name = ? AND id != ?", *req.Name, groupID).First(&existingGroup).Error; err == nil {
			return nil, errors.New("权限组名称已存在")
		}
		group.Name = *req.Name
	}

	if req.Description != nil {
		group.Description = *req.Description
	}

	if req.SortOrder != nil {
		group.SortOrder = *req.SortOrder
	}

	if err := group.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(group).Error; err != nil {
		return nil, err
	}

	return s.toPermissionGroupDTO(group, false), nil
}

// DeletePermissionGroup 删除权限组
func (s *PermissionGroupService) DeletePermissionGroup(groupID uint) error {
	group, err := s.GetPermissionGroupByID(groupID)
	if err != nil {
		return err
	}

	// 检查是否有关联的商品
	var productCount int64
	if err := s.db.Model(&model.Product{}).Where("permission_group_id = ?", groupID).Count(&productCount).Error; err != nil {
		return err
	}

	if productCount > 0 {
		return errors.New("该权限组下存在商品，无法删除")
	}

	if err := s.db.Delete(group).Error; err != nil {
		return err
	}

	return nil
}

// GetPermissionGroupNodes 获取权限组关联的节点
func (s *PermissionGroupService) GetPermissionGroupNodes(groupID uint) ([]*model.Node, error) {
	group, err := s.GetPermissionGroupByID(groupID)
	if err != nil {
		return nil, err
	}

	if err := s.db.Preload("Nodes").Find(&group).Error; err != nil {
		return nil, err
	}

	nodes := make([]*model.Node, len(group.Nodes))
	for i, node := range group.Nodes {
		nodes[i] = &node
	}

	return nodes, nil
}

// AddNodeToPermissionGroup 向权限组添加节点
func (s *PermissionGroupService) AddNodeToPermissionGroup(groupID uint, nodeID uint) error {
	group, err := s.GetPermissionGroupByID(groupID)
	if err != nil {
		return err
	}

	// 检查节点是否存在
	var node model.Node
	if err := s.db.First(&node, nodeID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("节点不存在")
		}
		return err
	}

	// 检查是否已经关联
	var count int64
	if err := s.db.Table("permission_group_nodes").
		Where("permission_group_id = ? AND node_id = ?", groupID, nodeID).
		Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		return errors.New("节点已经关联到该权限组")
	}

	// 创建关联
	if err := s.db.Exec(
		"INSERT INTO permission_group_nodes (permission_group_id, node_id) VALUES (?, ?)",
		groupID, nodeID,
	).Error; err != nil {
		return err
	}

	return nil
}

// RemoveNodeFromPermissionGroup 从权限组移除节点
func (s *PermissionGroupService) RemoveNodeFromPermissionGroup(groupID uint, nodeID uint) error {
	group, err := s.GetPermissionGroupByID(groupID)
	if err != nil {
		return err
	}

	// 检查节点是否存在
	var node model.Node
	if err := s.db.First(&node, nodeID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("节点不存在")
		}
		return err
	}

	// 删除关联
	if err := s.db.Exec(
		"DELETE FROM permission_group_nodes WHERE permission_group_id = ? AND node_id = ?",
		groupID, nodeID,
	).Error; err != nil {
		return err
	}

	return nil
}

// SetPermissionGroupNodes 设置权限组的节点（批量替换）
func (s *PermissionGroupService) SetPermissionGroupNodes(groupID uint, nodeIDs []uint) error {
	group, err := s.GetPermissionGroupByID(groupID)
	if err != nil {
		return err
	}

	// 检查所有节点是否存在
	for _, nodeID := range nodeIDs {
		var node model.Node
		if err := s.db.First(&node, nodeID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("节点ID %d 不存在", nodeID)
			}
			return err
		}
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除现有关联
	if err := tx.Exec(
		"DELETE FROM permission_group_nodes WHERE permission_group_id = ?",
		groupID,
	).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 添加新的关联
	if len(nodeIDs) > 0 {
		for _, nodeID := range nodeIDs {
			if err := tx.Exec(
				"INSERT INTO permission_group_nodes (permission_group_id, node_id) VALUES (?, ?)",
				groupID, nodeID,
			).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// GetPermissionGroupWithDetails 获取权限组详细信息（包含节点和商品）
func (s *PermissionGroupService) GetPermissionGroupWithDetails(groupID uint) (*PermissionGroupDTO, error) {
	group, err := s.GetPermissionGroupByID(groupID)
	if err != nil {
		return nil, err
	}

	// 预加载关联数据
	if err := s.db.Preload("Nodes").Preload("Products").First(&group, groupID).Error; err != nil {
		return nil, err
	}

	return s.toPermissionGroupDTO(group, true), nil
}

// GetPermissionGroupProducts 获取权限组关联的商品
func (s *PermissionGroupService) GetPermissionGroupProducts(groupID uint) ([]*model.Product, error) {
	group, err := s.GetPermissionGroupByID(groupID)
	if err != nil {
		return nil, err
	}

	if err := s.db.Preload("Products").Find(&group).Error; err != nil {
		return nil, err
	}

	products := make([]*model.Product, len(group.Products))
	for i, product := range group.Products {
		products[i] = &product
	}

	return products, nil
}

// GetUserPermissionGroups 获取用户的权限组
func (s *PermissionGroupService) GetUserPermissionGroups(userID uint) ([]*model.PermissionGroup, error) {
	// 通过用户的订阅获取权限组
	var userSubscriptions []model.UserSubscription
	if err := s.db.Where("user_id = ? AND status = ?", userID, "active").
		Preload("Product.PermissionGroup").
		Find(&userSubscriptions).Error; err != nil {
		return nil, err
	}

	// 去重
	groupMap := make(map[uint]*model.PermissionGroup)
	for _, subscription := range userSubscriptions {
		if subscription.Product.PermissionGroupID != 0 {
			groupMap[subscription.Product.PermissionGroupID] = &subscription.Product.PermissionGroup
		}
	}

	groups := make([]*model.PermissionGroup, 0, len(groupMap))
	for _, group := range groupMap {
		groups = append(groups, group)
	}

	return groups, nil
}

// CheckUserNodePermission 检查用户是否有权限访问节点
func (s *PermissionGroupService) CheckUserNodePermission(userID uint, nodeID uint) (bool, error) {
	// 获取用户的权限组
	groups, err := s.GetUserPermissionGroups(userID)
	if err != nil {
		return false, err
	}

	// 检查节点是否在用户的任何一个权限组中
	for _, group := range groups {
		var count int64
		if err := s.db.Table("permission_group_nodes").
			Where("permission_group_id = ? AND node_id = ?", group.ID, nodeID).
			Count(&count).Error; err != nil {
			return false, err
		}

		if count > 0 {
			return true, nil
		}
	}

	return false, nil
}

// GetUserNodes 获取用户有权限访问的节点
func (s *PermissionGroupService) GetUserNodes(userID uint) ([]*model.Node, error) {
	// 获取用户的权限组
	groups, err := s.GetUserPermissionGroups(userID)
	if err != nil {
		return nil, err
	}

	if len(groups) == 0 {
		return []*model.Node{}, nil
	}

	// 构建权限组ID列表
	groupIDs := make([]uint, len(groups))
	for i, group := range groups {
		groupIDs[i] = group.ID
	}

	// 查询这些权限组关联的所有节点
	var nodes []model.Node
	if err := s.db.Table("nodes").
		Select("nodes.*").
		Joins("INNER JOIN permission_group_nodes ON nodes.id = permission_group_nodes.node_id").
		Where("permission_group_nodes.permission_group_id IN ?", groupIDs).
		Where("nodes.status = ?", "online").
		Order("nodes.sort_order ASC, nodes.id ASC").
		Find(&nodes).Error; err != nil {
		return nil, err
	}

	// 去重
	nodeMap := make(map[uint]*model.Node)
	for i := range nodes {
		nodeMap[nodes[i].ID] = &nodes[i]
	}

	result := make([]*model.Node, 0, len(nodeMap))
	for _, node := range nodeMap {
		result = append(result, node)
	}

	return result, nil
}

// 辅助函数
func (s *PermissionGroupService) toPermissionGroupDTO(group *model.PermissionGroup, includeDetails bool) *PermissionGroupDTO {
	dto := &PermissionGroupDTO{
		ID:          group.ID,
		Name:        group.Name,
		Description: group.Description,
		SortOrder:   group.SortOrder,
		CreatedAt:   group.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   group.UpdatedAt.Format("2006-01-02 15:04:05"),
		NodeCount:   len(group.Nodes),
	}

	if includeDetails {
		dto.Nodes = make([]*NodeDTO, len(group.Nodes))
		for i, node := range group.Nodes {
			dto.Nodes[i] = &NodeDTO{
				ID:         node.ID,
				Name:       node.Name,
				Protocol:   node.Protocol,
				Host:       node.Host,
				Port:       node.Port,
				Status:     node.Status,
				SortOrder:  node.SortOrder,
				CreatedAt:  node.CreatedAt.Format("2006-01-02 15:04:05"),
			}
		}

		dto.Products = make([]*ProductDTO, len(group.Products))
		for i, product := range group.Products {
			dto.Products[i] = &ProductDTO{
				ID:           product.ID,
				Name:         product.Name,
				Description:  product.Description,
				Price:        product.Price,
				TrafficLimit: product.TrafficLimit,
				DurationDays: product.DurationDays,
				DeviceLimit:  product.DeviceLimit,
				SpeedLimit:   product.SpeedLimit,
				Status:       product.Status,
				SortOrder:    product.SortOrder,
				CreatedAt:    product.CreatedAt.Format("2006-01-02 15:04:05"),
			}
		}
	}

	return dto
}

// NodeDTO 节点数据传输对象
type NodeDTO struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	Protocol   string `json:"protocol"`
	Host       string `json:"host"`
	Port       int    `json:"port"`
	Status     string `json:"status"`
	SortOrder  int    `json:"sort_order"`
	CreatedAt  string `json:"created_at"`
}

// ProductDTO 商品数据传输对象
type ProductDTO struct {
	ID           uint    `json:"id"`
	Name         string  `json:"name"`
	Description  string  `json:"description"`
	Price        float64 `json:"price"`
	TrafficLimit int64   `json:"traffic_limit"`
	DurationDays int     `json:"duration_days"`
	DeviceLimit  int     `json:"device_limit"`
	SpeedLimit   int     `json:"speed_limit"`
	Status       string  `json:"status"`
	SortOrder    int     `json:"sort_order"`
	CreatedAt    string  `json:"created_at"`
}