package model

import (
	"fmt"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	UUID         string         `json:"uuid" gorm:"uniqueIndex;size:36"`
	Username     string         `json:"username" gorm:"uniqueIndex;size:50"`
	Email        string         `json:"email" gorm:"uniqueIndex;size:100"`
	PasswordHash string         `json:"-" gorm:"size:255"`
	Role         string         `json:"role" gorm:"type:enum('admin','user');default:'user'"`
	Status       string         `json:"status" gorm:"type:enum('active','inactive','expired');default:'active'"`
	TrafficLimit int64          `json:"traffic_limit" gorm:"default:0"`
	TrafficUsed  int64          `json:"traffic_used" gorm:"default:0"`
	DeviceLimit  int            `json:"device_limit" gorm:"default:1"`
	SpeedLimit   int            `json:"speed_limit" gorm:"default:0"`
	ExpiredAt    *time.Time     `json:"expired_at"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Nodes             []Node             `json:"nodes" gorm:"many2many:user_nodes;"`
	UserSubscriptions []UserSubscription `json:"user_subscriptions" gorm:"foreignKey:UserID"`
	Orders            []Order            `json:"orders" gorm:"foreignKey:UserID"`
}

// Node 节点模型
type Node struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:100"`
	Protocol    string         `json:"protocol" gorm:"size:20;default:'anytls'"`
	Host        string         `json:"host" gorm:"size:255"`
	Port        int            `json:"port"`
	Password    string         `json:"password" gorm:"size:255"`
	Config      datatypes.JSON `json:"config"` // 协议特定配置
	ServerName  string         `json:"server_name" gorm:"size:255"`
	Status      string         `json:"status" gorm:"type:enum('online','offline','maintenance');default:'offline'"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	TrafficRate float64        `json:"traffic_rate" gorm:"type:decimal(10,2);default:1.0"`
	MaxUsers    int            `json:"max_users" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Users []User `json:"users" gorm:"many2many:user_nodes;"`
}

// PermissionGroup 权限组模型
type PermissionGroup struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"uniqueIndex;size:100"`
	Description string         `json:"description" gorm:"type:text"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Nodes    []Node    `json:"nodes" gorm:"many2many:permission_group_nodes;"`
	Products []Product `json:"products" gorm:"foreignKey:PermissionGroupID"`
}

// Product 订阅商品模型
type Product struct {
	ID                  uint           `json:"id" gorm:"primaryKey"`
	Name                string         `json:"name" gorm:"size:100"`
	Description         string         `json:"description" gorm:"type:text"`
	Price               float64        `json:"price" gorm:"type:decimal(10,2)"`
	TrafficLimit        int64          `json:"traffic_limit"`
	DurationDays        int            `json:"duration_days"`
	DeviceLimit         int            `json:"device_limit" gorm:"default:1"`
	SpeedLimit          int            `json:"speed_limit" gorm:"default:0"`
	PermissionGroupID   uint           `json:"permission_group_id"`
	Status              string         `json:"status" gorm:"type:enum('active','inactive');default:'active'"`
	SortOrder           int            `json:"sort_order" gorm:"default:0"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	PermissionGroup   PermissionGroup    `json:"permission_group" gorm:"foreignKey:PermissionGroupID"`
	UserSubscriptions []UserSubscription `json:"user_subscriptions" gorm:"foreignKey:ProductID"`
	Orders            []Order            `json:"orders" gorm:"foreignKey:ProductID"`
}

// UserSubscription 用户订阅模型
type UserSubscription struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id"`
	ProductID uint           `json:"product_id"`
	OrderID   string         `json:"order_id" gorm:"size:50"`
	Status    string         `json:"status" gorm:"type:enum('active','expired','cancelled');default:'active'"`
	StartedAt time.Time      `json:"started_at"`
	ExpiredAt time.Time      `json:"expired_at"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User    User    `json:"user" gorm:"foreignKey:UserID"`
	Product Product `json:"product" gorm:"foreignKey:ProductID"`
}

// Order 订单模型
type Order struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	OrderID       string         `json:"order_id" gorm:"uniqueIndex;size:50"`
	UserID        uint           `json:"user_id"`
	ProductID     uint           `json:"product_id"`
	Amount        float64        `json:"amount" gorm:"type:decimal(10,2)"`
	Status        string         `json:"status" gorm:"type:enum('pending','paid','cancelled','refunded');default:'pending'"`
	PaymentMethod string         `json:"payment_method" gorm:"size:50"`
	PaymentID     string         `json:"payment_id" gorm:"size:100"`
	PaidAt        *time.Time     `json:"paid_at"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User    User    `json:"user" gorm:"foreignKey:UserID"`
	Product Product `json:"product" gorm:"foreignKey:ProductID"`
}

// TrafficLog 流量统计模型
type TrafficLog struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id"`
	NodeID     uint      `json:"node_id"`
	Upload     int64     `json:"upload" gorm:"default:0"`
	Download   int64     `json:"download" gorm:"default:0"`
	RecordedAt time.Time `json:"recorded_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Node Node `json:"node" gorm:"foreignKey:NodeID"`
}

// OnlineUser 在线用户模型
type OnlineUser struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id"`
	NodeID      uint      `json:"node_id"`
	IPAddress   string    `json:"ip_address" gorm:"size:45"`
	ConnectedAt time.Time `json:"connected_at"`
	LastSeen    time.Time `json:"last_seen"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Node Node `json:"node" gorm:"foreignKey:NodeID"`
}

// Validate 验证用户模型
func (u *User) Validate() error {
	if u.Username == "" {
		return fmt.Errorf("用户名不能为空")
	}
	if u.Email == "" {
		return fmt.Errorf("邮箱不能为空")
	}
	if u.PasswordHash == "" {
		return fmt.Errorf("密码哈希不能为空")
	}
	if u.Role != "admin" && u.Role != "user" {
		return fmt.Errorf("无效的用户角色")
	}
	if u.DeviceLimit < 0 {
		return fmt.Errorf("设备限制不能为负数")
	}
	if u.SpeedLimit < 0 {
		return fmt.Errorf("速度限制不能为负数")
	}
	return nil
}

// ToSafeJSON 返回安全的用户JSON数据（不包含敏感信息）
func (u *User) ToSafeJSON() map[string]interface{} {
	return map[string]interface{}{
		"id":            u.ID,
		"uuid":          u.UUID,
		"username":      u.Username,
		"email":         u.Email,
		"role":          u.Role,
		"status":        u.Status,
		"traffic_limit": u.TrafficLimit,
		"traffic_used":  u.TrafficUsed,
		"device_limit":  u.DeviceLimit,
		"speed_limit":   u.SpeedLimit,
		"expired_at":    u.ExpiredAt,
		"created_at":    u.CreatedAt,
		"updated_at":    u.UpdatedAt,
	}
}

// Validate 验证节点模型
func (n *Node) Validate() error {
	if n.Name == "" {
		return fmt.Errorf("节点名称不能为空")
	}
	if n.Host == "" {
		return fmt.Errorf("节点地址不能为空")
	}
	if n.Port <= 0 || n.Port > 65535 {
		return fmt.Errorf("无效的端口号")
	}
	if n.Password == "" {
		return fmt.Errorf("节点密码不能为空")
	}
	if n.Protocol == "" {
		return fmt.Errorf("节点协议不能为空")
	}
	if n.Status != "online" && n.Status != "offline" && n.Status != "maintenance" {
		return fmt.Errorf("无效的节点状态")
	}
	if n.MaxUsers < 0 {
		return fmt.Errorf("最大用户数不能为负数")
	}
	if n.TrafficRate <= 0 {
		return fmt.Errorf("流量倍率必须大于0")
	}
	return nil
}

// GetAvailableTraffic 获取用户可用流量
func (u *User) GetAvailableTraffic() int64 {
	if u.TrafficLimit == 0 {
		return 0 // 无限流量
	}
	return u.TrafficLimit - u.TrafficUsed
}

// IsExpired 检查用户是否过期
func (u *User) IsExpired() bool {
	if u.ExpiredAt == nil {
		return false
	}
	return time.Now().After(*u.ExpiredAt)
}

// IsActive 检查用户是否活跃
func (u *User) IsActive() bool {
	return u.Status == "active" && !u.IsExpired()
}

// CanConnect 检查用户是否可以连接
func (u *User) CanConnect() bool {
	if !u.IsActive() {
		return false
	}
	
	// 检查流量限制
	if u.TrafficLimit > 0 && u.TrafficUsed >= u.TrafficLimit {
		return false
	}
	
	// 检查设备限制（需要在线查询）
	// 这里需要在实际使用时查询在线用户表
	
	return true
}

// GetSubscriptionStatus 获取用户订阅状态
func (u *User) GetSubscriptionStatus() string {
	if u.IsExpired() {
		return "expired"
	}
	if u.Status == "inactive" {
		return "inactive"
	}
	if u.Status == "active" {
		if u.TrafficLimit > 0 && u.TrafficUsed >= u.TrafficLimit {
			return "traffic_exceeded"
		}
		return "active"
	}
	return "unknown"
}

// Validate 验证权限组模型
func (pg *PermissionGroup) Validate() error {
	if pg.Name == "" {
		return fmt.Errorf("权限组名称不能为空")
	}
	if len(pg.Name) > 100 {
		return fmt.Errorf("权限组名称不能超过100个字符")
	}
	return nil
}

// Validate 验证商品模型
func (p *Product) Validate() error {
	if p.Name == "" {
		return fmt.Errorf("商品名称不能为空")
	}
	if p.Price < 0 {
		return fmt.Errorf("商品价格不能为负数")
	}
	if p.DurationDays <= 0 {
		return fmt.Errorf("商品有效期必须大于0天")
	}
	if p.DeviceLimit < 0 {
		return fmt.Errorf("设备限制不能为负数")
	}
	if p.SpeedLimit < 0 {
		return fmt.Errorf("速度限制不能为负数")
	}
	if p.PermissionGroupID == 0 {
		return fmt.Errorf("必须指定权限组")
	}
	if p.Status != "active" && p.Status != "inactive" {
		return fmt.Errorf("无效的商品状态")
	}
	return nil
}

// Validate 验证订单模型
func (o *Order) Validate() error {
	if o.OrderID == "" {
		return fmt.Errorf("订单ID不能为空")
	}
	if o.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if o.ProductID == 0 {
		return fmt.Errorf("商品ID不能为空")
	}
	if o.Amount < 0 {
		return fmt.Errorf("订单金额不能为负数")
	}
	if o.Status != "pending" && o.Status != "paid" && o.Status != "cancelled" && o.Status != "refunded" {
		return fmt.Errorf("无效的订单状态")
	}
	return nil
}

// Validate 验证用户订阅模型
func (us *UserSubscription) Validate() error {
	if us.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if us.ProductID == 0 {
		return fmt.Errorf("商品ID不能为空")
	}
	if us.Status != "active" && us.Status != "expired" && us.Status != "cancelled" {
		return fmt.Errorf("无效的订阅状态")
	}
	if us.ExpiredAt.IsZero() {
		return fmt.Errorf("过期时间不能为空")
	}
	return nil
}

// Validate 验证流量日志模型
func (tl *TrafficLog) Validate() error {
	if tl.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if tl.NodeID == 0 {
		return fmt.Errorf("节点ID不能为空")
	}
	if tl.Upload < 0 {
		return fmt.Errorf("上传流量不能为负数")
	}
	if tl.Download < 0 {
		return fmt.Errorf("下载流量不能为负数")
	}
	if tl.RecordedAt.IsZero() {
		return fmt.Errorf("记录时间不能为空")
	}
	return nil
}

// Validate 验证在线用户模型
func (ou *OnlineUser) Validate() error {
	if ou.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if ou.NodeID == 0 {
		return fmt.Errorf("节点ID不能为空")
	}
	if ou.IPAddress == "" {
		return fmt.Errorf("IP地址不能为空")
	}
	if ou.ConnectedAt.IsZero() {
		return fmt.Errorf("连接时间不能为空")
	}
	if ou.LastSeen.IsZero() {
		return fmt.Errorf("最后活跃时间不能为空")
	}
	return nil
}

// IsExpired 检查订阅是否过期
func (us *UserSubscription) IsExpired() bool {
	return time.Now().After(us.ExpiredAt)
}

// IsActive 检查订阅是否活跃
func (us *UserSubscription) IsActive() bool {
	return us.Status == "active" && !us.IsExpired()
}

// GetRemainingDays 获取订阅剩余天数
func (us *UserSubscription) GetRemainingDays() int {
	if us.IsExpired() {
		return 0
	}
	duration := time.Until(us.ExpiredAt)
	return int(duration.Hours() / 24)
}

// IsOnline 检查用户是否在线
func (ou *OnlineUser) IsOnline() bool {
	// 如果超过5分钟没有活跃，认为离线
	return time.Since(ou.LastSeen) < 5*time.Minute
}

// GetSessionDuration 获取会话持续时间
func (ou *OnlineUser) GetSessionDuration() time.Duration {
	return ou.LastSeen.Sub(ou.ConnectedAt)
}

// GetTotalTraffic 获取总流量
func (tl *TrafficLog) GetTotalTraffic() int64 {
	return tl.Upload + tl.Download
}

// GetActiveSubscription 获取用户当前有效订阅
func (u *User) GetActiveSubscription(db *gorm.DB) (*UserSubscription, error) {
	var subscription UserSubscription
	err := db.Where("user_id = ? AND status = 'active' AND expired_at > ?", 
		u.ID, time.Now()).First(&subscription).Error
	if err != nil {
		return nil, err
	}
	return &subscription, nil
}

// GetAccessibleNodes 获取用户可访问的节点（基于权限组）
func (u *User) GetAccessibleNodes(db *gorm.DB) ([]Node, error) {
	subscription, err := u.GetActiveSubscription(db)
	if err != nil {
		return nil, err
	}
	
	var nodes []Node
	err = db.Joins("JOIN permission_group_nodes ON nodes.id = permission_group_nodes.node_id").
		Joins("JOIN products ON products.permission_group_id = permission_group_nodes.group_id").
		Where("products.id = ? AND nodes.status = 'online'", subscription.ProductID).
		Find(&nodes).Error
	
	return nodes, err
}