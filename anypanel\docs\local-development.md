# 本地开发环境配置

## 系统要求

- Go 1.21+
- Node.js 18+
- MySQL 8.0+
- Redis 7.0+

## 本地开发环境设置

### 1. 安装依赖

#### 后端依赖
```bash
cd backend
go mod tidy
```

#### 前端依赖
```bash
cd frontend
npm install
```

### 2. 数据库设置

#### 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE IF NOT EXISTS anypanel CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 退出MySQL
exit
```

#### 导入初始数据
```bash
# 从SQL文件导入
mysql -u root -p anypanel < scripts/init.sql

# 或者使用后端应用自动迁移
cd backend
go run cmd/main.go
```

### 3. Redis设置

确保Redis服务正在运行：
```bash
# Windows (如果使用Chocolatey)
redis-server

# Linux/Mac
sudo systemctl start redis
# 或者
redis-server
```

### 4. 配置文件

#### 后端配置
编辑 `backend/config.yaml` 文件，修改数据库密码：

```yaml
database:
  host: "localhost"
  port: 3306
  username: "root"
  password: "your_mysql_password"  # 修改为你的MySQL密码
  database: "anypanel"
```

#### 前端配置
创建或编辑 `frontend/.env.development`：

```env
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_TITLE=AnyPanel
```

### 5. 启动开发环境

#### 方式一：分别启动

1. **启动后端服务**
```bash
cd backend
go run cmd/main.go
```

2. **启动前端服务**
```bash
cd frontend
npm run dev
```

#### 方式二：使用启动脚本

**Windows:**
```bash
.\scripts\dev-local.bat
```

**Linux/Mac:**
```bash
chmod +x scripts/dev-local.sh
./scripts/dev-local.sh
```

#### 方式三：使用Air热重载（后端）

1. **安装Air**
```bash
go install github.com/cosmtrek/air@latest
```

2. **启动后端热重载**
```bash
cd backend
air -c ../.air.toml
```

3. **启动前端**
```bash
cd frontend
npm run dev
```

### 6. 访问应用

- **管理面板**: http://localhost:3000
- **API服务**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger/index.html

### 7. 默认账户

- **用户名**: admin
- **密码**: password

## 开发工具

### VS Code 配置

推荐安装的插件：
- Go (golang.go)
- ESLint (dbaeumer.vscode-eslint)
- Prettier (esbenp.prettier-vscode)
- Docker (ms-azuretools.vscode-docker)
- GitLens (eamodio.gitlens)

### 调试配置

使用VS Code的调试功能，配置文件位于 `.vscode/launch.json`。

## 常见问题

### 数据库连接失败

1. 确保MySQL服务正在运行
2. 检查数据库配置是否正确
3. 确认数据库已创建
4. 检查用户权限

### Redis连接失败

1. 确保Redis服务正在运行
2. 检查Redis配置是否正确

### 前端启动失败

1. 确保Node.js版本符合要求
2. 删除node_modules重新安装
3. 检查端口是否被占用

### 后端启动失败

1. 确保Go版本符合要求
2. 检查依赖是否安装完成
3. 确认配置文件格式正确

## 开发流程

1. **代码修改**
   - 后端：修改Go代码，Air会自动重启
   - 前端：修改React代码，Vite会自动热重载

2. **数据库迁移**
   - 修改模型文件后，重启后端服务自动迁移

3. **测试**
   - 访问对应URL测试功能
   - 查看日志排查问题

4. **调试**
   - 使用VS Code调试功能
   - 查看控制台输出和日志