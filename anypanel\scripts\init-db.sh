#!/bin/bash

echo "🗄️ 初始化本地数据库..."

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_NAME="anypanel"

# 获取数据库密码
read -sp "请输入MySQL root密码: " DB_PASSWORD
echo ""

# 检查MySQL连接
echo "🔍 检查MySQL连接..."
if ! mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &>/dev/null; then
    echo "❌ MySQL连接失败，请检查配置"
    exit 1
fi

echo "✅ MySQL连接成功"

# 创建数据库
echo "📊 创建数据库..."
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if [ $? -eq 0 ]; then
    echo "✅ 数据库创建成功"
else
    echo "❌ 数据库创建失败"
    exit 1
fi

# 导入初始数据
echo "📥 导入初始数据..."
if [ -f "scripts/init.sql" ]; then
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < scripts/init.sql
    
    if [ $? -eq 0 ]; then
        echo "✅ 初始数据导入成功"
    else
        echo "❌ 初始数据导入失败"
        exit 1
    fi
else
    echo "❌ 初始化SQL文件不存在: scripts/init.sql"
    exit 1
fi

echo "🎉 数据库初始化完成！"
echo ""
echo "💡 现在可以启动开发环境了："
echo "   Windows: .\\scripts\\dev-local.bat"
echo "   Linux/Mac: ./scripts/dev-local.sh"